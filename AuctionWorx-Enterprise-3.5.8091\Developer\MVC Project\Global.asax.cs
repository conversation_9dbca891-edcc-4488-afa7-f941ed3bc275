using AutoMapper;
using CsvHelper;
using CsvHelper.Configuration.Attributes;
using DuoVia.FuzzyStrings;
using Google.Apis.Auth.OAuth2;
using Google.Apis.Services;
using Google.Apis.ShoppingContent.v2_1;
using Microsoft.Ajax.Utilities;
using Microsoft.AspNet.Identity;
using Microsoft.AspNet.Identity.Owin;
using Microsoft.AspNet.SignalR;
using Microsoft.AspNet.SignalR.Hosting;
using Microsoft.Owin.Security;
using Microsoft.Practices.Unity.Configuration.ConfigurationHelpers;
using Newtonsoft.Json.Linq;
using RainWorx.FrameWorx.BLL.ServiceImplementations;
using RainWorx.FrameWorx.Clients;
using RainWorx.FrameWorx.DAL;
using RainWorx.FrameWorx.DTO;
using RainWorx.FrameWorx.DTO.EventArgs;
using RainWorx.FrameWorx.DTO.FaultContracts;
using RainWorx.FrameWorx.MVC.Areas.API.Models;
using RainWorx.FrameWorx.MVC.Controllers;
using RainWorx.FrameWorx.MVC.Helpers;
using RainWorx.FrameWorx.MVC.Helpers.GoogleShopping;
using RainWorx.FrameWorx.MVC.Models;
using RainWorx.FrameWorx.MVC.Models.Framework;
using RainWorx.FrameWorx.MVC.Models.RequestModels;
using RainWorx.FrameWorx.Providers.Fee;
using RainWorx.FrameWorx.Providers.Listing;
using RainWorx.FrameWorx.Providers.MediaAsset;
using RainWorx.FrameWorx.Providers.Payment;
using RainWorx.FrameWorx.Queueing;
using RainWorx.FrameWorx.Strings;
using RainWorx.FrameWorx.Unity;
using RainWorx.FrameWorx.Utility;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Configuration;
using System.Data.Entity;
using System.Data.Entity.Infrastructure;
using System.Diagnostics;
using System.Globalization;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
//using Microsoft.Web.WebPages.OAuth;
using System.Net;
using System.Net.Http;
using System.Net.PeerToPeer;
using System.Reflection;
using System.Security.Policy;
using System.ServiceModel;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;
using System.Web;
using System.Web.Helpers;
using System.Web.Hosting;
using System.Web.Http;
using System.Web.Mvc;
using System.Web.Optimization;
using System.Web.Routing;
using System.Web.Script.Serialization;
using System.Web.Security;
using System.Web.UI.WebControls;
using Twilio.Types;
using AccountController = RainWorx.FrameWorx.MVC.Controllers.AccountController;
using Invoice = RainWorx.FrameWorx.DTO.Invoice;

namespace RainWorx.FrameWorx.MVC
{
    // Note: For instructions on enabling IIS7 classic mode, 
    // visit http://go.microsoft.com/fwlink/?LinkId=301868
    public class WebApiApplication : System.Web.HttpApplication
    {
        private SchedulerService _schedulerService;
        private NotifierService _notifierService;
        private IQueueManager _queueManager;

        protected void Application_Start()
        {
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();

            bool ensureTls12Support = false;
            bool.TryParse(ConfigurationManager.AppSettings["EnsureTls12Support"], out ensureTls12Support);
            if (ensureTls12Support) ServicePointManager.SecurityProtocol |= SecurityProtocolType.Tls12;

            RainWorx.FrameWorx.MVC.Areas.API.Utilities.VirtualRoot = Server.MapPath("~");

            AreaRegistration.RegisterAllAreas();

            WebApiConfig.Register(GlobalConfiguration.Configuration);
            //GlobalConfiguration.Configure(WebApiConfig.Register);
            FilterConfig.RegisterGlobalFilters(GlobalFilters.Filters);

            RouteConfig.RegisterRoutes(RouteTable.Routes, SiteClient.TextSetting(SiteProperties.HomepageContent));

            BundleConfig.RegisterBundles(BundleTable.Bundles);

            bool siteClientResetNeeded = false;
            bool somethingChanged;

            do
            {
                somethingChanged = CommonClient.Initialize(Strings.SystemActors.SystemUserName);
            } while (somethingChanged);

            CommonClient.RegisterMediaAssetProviders();

            siteClientResetNeeded = siteClientResetNeeded | somethingChanged;

            do
            {
                somethingChanged = false;
                foreach (IMediaGenerator generator in UnityResolver.UnityContainer.ResolveAll(typeof(IMediaGenerator)))
                {
                    somethingChanged = generator.RegisterSelf("StartupRegistrar") || somethingChanged;
                }
                foreach (IFeeProcessor processor in UnityResolver.UnityContainer.ResolveAll(typeof(IFeeProcessor)))
                {
                    somethingChanged = processor.RegisterSelf("StartupRegistrar") || somethingChanged;
                }
                foreach (IListing listing in UnityResolver.UnityContainer.ResolveAll(typeof(IListing)))
                {
                    somethingChanged = listing.RegisterSelf("StartupRegistrar") || somethingChanged;
                }
            } while (somethingChanged);

            siteClientResetNeeded = siteClientResetNeeded | somethingChanged;

            //call each payment provider RegisterSelf() method
            somethingChanged = false;
            foreach (IPaymentProvider paymentProvider in UnityResolver.UnityContainer.ResolveAll(typeof(IPaymentProvider)))
            {
                somethingChanged = paymentProvider.RegisterSelf();
            }

            siteClientResetNeeded = siteClientResetNeeded | somethingChanged;

            bool backgroundThreadsEnabled = true;
            bool tempBool1;
            if (bool.TryParse(ConfigurationManager.AppSettings["BackgroundThreadsEnabled"], out tempBool1))
            {
                backgroundThreadsEnabled = tempBool1;
            }
            //If FrameWorx is running InProcess (In IIS, by itself...)
            if (Clients.ClientFactory.ExecutionContext == Clients.ClientFactory.ExecutionContextEnum.InProcess)
            {
                if (backgroundThreadsEnabled)
                {
                    _schedulerService = new SchedulerService();
                    _schedulerService.StartThreads(Server.MapPath("~"));

                    _notifierService = new NotifierService();
                    _notifierService.StartThreads();
                }
            }

            //run any custom application initializers
            foreach (ICustomApplicationInitializer initializer in UnityResolver.UnityContainer.ResolveAll(typeof(ICustomApplicationInitializer)))
            {
                initializer.InitializeApplication();
            }

            SiteClient.UpdateCustomCurrencies(ConfigurationManager.AppSettings["CustomCurrencies"]);

            //detect connection string for an rw7 import and enable site property, if applicable
            if (ConfigurationManager.ConnectionStrings["db_connection_rw7"] != null)
            {
                if (!SiteClient.BoolSetting(Strings.SiteProperties.ImportUsersEnabled) ||
                    !SiteClient.BoolSetting(Strings.SiteProperties.ImportCategoriesEnabled) ||
                    !SiteClient.BoolSetting(Strings.SiteProperties.ImportListingsEnabled) ||
                    !SiteClient.BoolSetting(Strings.SiteProperties.ImportRegionsEnabled) ||
                    !SiteClient.BoolSetting(Strings.SiteProperties.ImportFeedbacksEnabled))
                {
                    SiteClient.UpdateSetting(Strings.SystemActors.SystemUserName, Strings.SiteProperties.ImportUsersEnabled, true.ToString(), "en");
                    SiteClient.UpdateSetting(Strings.SystemActors.SystemUserName, Strings.SiteProperties.ImportCategoriesEnabled, true.ToString(), "en");
                    SiteClient.UpdateSetting(Strings.SystemActors.SystemUserName, Strings.SiteProperties.ImportListingsEnabled, true.ToString(), "en");
                    SiteClient.UpdateSetting(Strings.SystemActors.SystemUserName, Strings.SiteProperties.ImportRegionsEnabled, true.ToString(), "en");
                    SiteClient.UpdateSetting(Strings.SystemActors.SystemUserName, Strings.SiteProperties.ImportFeedbacksEnabled, true.ToString(), "en");
                    siteClientResetNeeded = true;
                }
            }
            else if (SiteClient.BoolSetting(Strings.SiteProperties.ImportUsersEnabled) ||
                    SiteClient.BoolSetting(Strings.SiteProperties.ImportCategoriesEnabled) ||
                    SiteClient.BoolSetting(Strings.SiteProperties.ImportListingsEnabled) ||
                    SiteClient.BoolSetting(Strings.SiteProperties.ImportRegionsEnabled) ||
                    SiteClient.BoolSetting(Strings.SiteProperties.ImportFeedbacksEnabled))
            {
                SiteClient.UpdateSetting(Strings.SystemActors.SystemUserName, Strings.SiteProperties.ImportUsersEnabled, false.ToString(), "en");
                SiteClient.UpdateSetting(Strings.SystemActors.SystemUserName, Strings.SiteProperties.ImportCategoriesEnabled, false.ToString(), "en");
                SiteClient.UpdateSetting(Strings.SystemActors.SystemUserName, Strings.SiteProperties.ImportListingsEnabled, false.ToString(), "en");
                SiteClient.UpdateSetting(Strings.SystemActors.SystemUserName, Strings.SiteProperties.ImportRegionsEnabled, false.ToString(), "en");
                SiteClient.UpdateSetting(Strings.SystemActors.SystemUserName, Strings.SiteProperties.ImportFeedbacksEnabled, false.ToString(), "en");
                siteClientResetNeeded = true;
            }

            //detect changed Active Directory web.config settings and propagate to site properties if applicable
            bool adEnabledInWebConfig = false;
            bool tempBool;
            if (bool.TryParse(ConfigurationManager.AppSettings["ActiveDirectoryEnabled"] ?? "false", out tempBool))
            {
                adEnabledInWebConfig = tempBool;
            }
            string adDomainInWebConfig = ConfigurationManager.AppSettings["ActiveDirectoryDomain"] ?? string.Empty;
            string adAdminInWebConfig = ConfigurationManager.AppSettings["ActiveDirectoryAdminUserName"] ?? string.Empty;
            if (SiteClient.BoolSetting(SiteProperties.ActiveDirectoryEnabled) != adEnabledInWebConfig ||
                SiteClient.TextSetting(SiteProperties.ActiveDirectoryDomain) != adDomainInWebConfig ||
                SiteClient.TextSetting(SiteProperties.ActiveDirectoryAdminUserName) != adAdminInWebConfig)
            {
                SiteClient.UpdateSetting(SystemActors.SystemUserName, SiteProperties.ActiveDirectoryEnabled, adEnabledInWebConfig.ToString(), "en");
                SiteClient.UpdateSetting(SystemActors.SystemUserName, SiteProperties.ActiveDirectoryDomain, adDomainInWebConfig, "en");
                SiteClient.UpdateSetting(SystemActors.SystemUserName, SiteProperties.ActiveDirectoryAdminUserName, adAdminInWebConfig, "en");
                siteClientResetNeeded = true;
            }

            bool buyerRemoveSaleButtonEnabled = false;
            if (bool.TryParse(ConfigurationManager.AppSettings["BuyerRemoveSaleButtonEnabled"] ?? "false", out tempBool))
            {
                buyerRemoveSaleButtonEnabled = tempBool;
            }
            if (SiteClient.BoolSetting(SiteProperties.BuyerRemoveSaleButtonEnabled) != buyerRemoveSaleButtonEnabled)
            {
                SiteClient.UpdateSetting(SystemActors.SystemUserName, SiteProperties.BuyerRemoveSaleButtonEnabled, buyerRemoveSaleButtonEnabled.ToString(), "en");
                siteClientResetNeeded = true;
            }

            //AutoSelectFirstShippingOptionOnAutoPay (true by default)
            bool autoSelectFirstShippingOptionOnAutoPay = true;
            if (bool.TryParse(ConfigurationManager.AppSettings[SiteProperties.AutoSelectFirstShippingOptionOnAutoPay] ?? "true", out tempBool))
            {
                autoSelectFirstShippingOptionOnAutoPay = tempBool;
            }
            if (SiteClient.BoolSetting(SiteProperties.AutoSelectFirstShippingOptionOnAutoPay) != autoSelectFirstShippingOptionOnAutoPay)
            {
                SiteClient.UpdateSetting(SystemActors.SystemUserName, SiteProperties.AutoSelectFirstShippingOptionOnAutoPay, autoSelectFirstShippingOptionOnAutoPay.ToString(), "en");
                siteClientResetNeeded = true;
            }

            //GetCatCountsForAllSearches (true by default)
            bool getCatCountsForAllSearches = true;
            if (bool.TryParse(ConfigurationManager.AppSettings[SiteProperties.GetCatCountsForAllSearches] ?? "true", out tempBool))
            {
                getCatCountsForAllSearches = tempBool;
            }
            if (SiteClient.BoolSetting(SiteProperties.GetCatCountsForAllSearches) != getCatCountsForAllSearches)
            {
                SiteClient.UpdateSetting(SystemActors.SystemUserName, SiteProperties.GetCatCountsForAllSearches, getCatCountsForAllSearches.ToString(), "en");
                siteClientResetNeeded = true;
            }

            //BaseUrl and SslEnabled
            string baseUrl = (ConfigurationManager.AppSettings["BaseUrl"] ?? string.Empty).ToLower();

            //for upgrade scenarios, in case this setting is missed in web.config, do not reset the SslEnabled, URL or SecureURL site props to the dafault value...
            if (baseUrl != "https://your_domain_name/")
            {
                if (baseUrl.EndsWith("/"))
                {
                    baseUrl = baseUrl.TrimEnd('/');
                }
                bool sslEnabled = baseUrl.StartsWith("https://");
                if (SiteClient.BoolSetting(SiteProperties.SslEnabled) != sslEnabled)
                {
                    SiteClient.UpdateSetting(SystemActors.SystemUserName, SiteProperties.SslEnabled, sslEnabled.ToString(), "en");
                    siteClientResetNeeded = true;

                    LogManager.WriteLog("new SSL status detected in web.config", "App Init Logic", "global.asax.cs", TraceEventType.Verbose, null, null,
                        new Dictionary<string, object>() {
                            { "old SslEnabled value", SiteClient.BoolSetting(SiteProperties.SslEnabled) },
                            { "new sslEnabled value", sslEnabled }
                        });
                }
                if (SiteClient.TextSetting(SiteProperties.URL) != baseUrl)
                {
                    SiteClient.UpdateSetting(SystemActors.SystemUserName, SiteProperties.URL, baseUrl, "en");
                    siteClientResetNeeded = true;

                    LogManager.WriteLog("new domain detected in web.config", "App Init Logic", "global.asax.cs", TraceEventType.Verbose, null, null,
                        new Dictionary<string, object>() {
                            { "old URL value", SiteClient.TextSetting(SiteProperties.URL) },
                            { "new URL value", baseUrl }
                        });
                }
                if (SiteClient.TextSetting(SiteProperties.SecureURL) != baseUrl)
                {
                    SiteClient.UpdateSetting(SystemActors.SystemUserName, SiteProperties.SecureURL, baseUrl, "en");
                    siteClientResetNeeded = true;

                    LogManager.WriteLog("new domain detected in web.config", "App Init Logic", "global.asax.cs", TraceEventType.Verbose, null, null,
                        new Dictionary<string, object>() {
                            { "old SecureURL value", SiteClient.TextSetting(SiteProperties.SecureURL) },
                            { "new SecureURL value", baseUrl }
                        });
                }
            }
            else
            {
                LogManager.WriteLog("invalid domain detected in web.config", "App Init Logic", "global.asax.cs", TraceEventType.Verbose, null, null,
                    new Dictionary<string, object>() {
                        { "configured value", baseUrl }
                    });
            }

            //AllowNewConsignorsFromExistingEmails
            bool allowNewConsignorsFromExistingEmails = false;
            tempBool = false;
            if (bool.TryParse(ConfigurationManager.AppSettings["AllowNewConsignorsFromExistingEmails"] ?? "false", out tempBool))
            {
                allowNewConsignorsFromExistingEmails = tempBool;
            }
            if (SiteClient.BoolSetting(SiteProperties.AllowNewConsignorsFromExistingEmails) != allowNewConsignorsFromExistingEmails)
            {
                SiteClient.UpdateSetting(SystemActors.SystemUserName, SiteProperties.AllowNewConsignorsFromExistingEmails, allowNewConsignorsFromExistingEmails.ToString(), "en");
                siteClientResetNeeded = true;
            }

            //SaveOriginalListingImages
            bool saveOriginalListingImages = false;
            tempBool = false;
            if (bool.TryParse(ConfigurationManager.AppSettings["SaveOriginalListingImages"] ?? "false", out tempBool))
            {
                saveOriginalListingImages = tempBool;
            }
            if (SiteClient.BoolSetting(SiteProperties.SaveOriginalListingImages) != saveOriginalListingImages)
            {
                SiteClient.UpdateSetting(SystemActors.SystemUserName, SiteProperties.SaveOriginalListingImages, saveOriginalListingImages.ToString(), "en");
                siteClientResetNeeded = true;
            }

            _queueManager = UnityResolver.Get<IQueueManager>();
            if (_queueManager.GetType() != typeof(QueueingDisabled))
            {
                if (!SiteClient.BoolSetting(Strings.SiteProperties.SignalREnabled) ||
                    !SiteClient.BoolSetting(Strings.SiteProperties.AsyncActionProcessing))
                {
                    bool sitePropResetNeeded = false;
                    if (!SiteClient.BoolSetting(SiteProperties.SignalREnabled))
                    {
                        SiteClient.UpdateSetting(Strings.SystemActors.SystemUserName, Strings.SiteProperties.SignalREnabled, true.ToString(), "en");
                        sitePropResetNeeded = true;
                    }
                    bool asyncBiddingRequested = true;
                    bool.TryParse(ConfigurationManager.AppSettings["AsyncActionProcessingEnabled"], out asyncBiddingRequested);
                    if (asyncBiddingRequested && !SiteClient.BoolSetting(SiteProperties.AsyncActionProcessing))
                    {
                        SiteClient.UpdateSetting(Strings.SystemActors.SystemUserName, Strings.SiteProperties.AsyncActionProcessing, true.ToString(), "en");
                        sitePropResetNeeded = true;
                    }
                    if (sitePropResetNeeded)
                    {
                        siteClientResetNeeded = true;
                    }
                }
            }
            else
            {
                if (SiteClient.BoolSetting(Strings.SiteProperties.SignalREnabled) ||
                    SiteClient.BoolSetting(Strings.SiteProperties.AsyncActionProcessing))
                {
                    //queueing is disabled but SignalR is enabled -- set applicable site properties
                    SiteClient.UpdateSetting(Strings.SystemActors.SystemUserName, Strings.SiteProperties.SignalREnabled, false.ToString(), "en");
                    SiteClient.UpdateSetting(Strings.SystemActors.SystemUserName, Strings.SiteProperties.AsyncActionProcessing, false.ToString(), "en");
                    siteClientResetNeeded = true;
                }
            }

            if (siteClientResetNeeded)
            {
                SiteClient.Reset();
            }

            //note: this must be called prior to any GlobalHost function calls because the underlying GlobalHost.HubPipeline.AddModule() will throw an exception if done afterwards
            GlobalConfiguration.Configuration.EnsureInitialized();

            bool rwx_SingleSigRListingCh = false;
            bool tempBool2;
            if (bool.TryParse(ConfigurationManager.AppSettings["SignalR_SingleListingChannel"], out tempBool2))
            {
                rwx_SingleSigRListingCh = tempBool2;
            }

            if (SiteClient.BoolSetting(SiteProperties.SignalREnabled))
            {
                if (!rwx_SingleSigRListingCh)
                {
                    if (!_queueManager.IsSimpleQueueArchitecture())
                    {
                        _queueManager.OnListingDTTMChange(Strings.QueueNames.ListingDTTMUpdateForSignalR, int.Parse(ConfigurationManager.AppSettings["ListingDTTMUpdateForSignalRThreads"]), data =>
                        {
                            //data.DTTMString = data.DTTM.AddHours(SiteClient.TimeZoneOffset).ToString(CultureInfo.InvariantCulture);
                            TimeZoneInfo siteTimeZone = TimeZoneInfo.FindSystemTimeZoneById(SiteClient.SiteTimeZone);
                            data.DTTMString = TimeZoneInfo.ConvertTime(data.DTTM, TimeZoneInfo.Utc, siteTimeZone).ToString(CultureInfo.InvariantCulture);
                            GlobalHost.ConnectionManager.GetHubContext<ListingHub>().Clients.Group(data.ListingID.ToString(CultureInfo.InvariantCulture)).updateListingDTTM(data);
                        });

                        _queueManager.OnListingActionChange(Strings.QueueNames.ListingActionUpdateForSignalR, int.Parse(ConfigurationManager.AppSettings["ListingActionUpdateForSignalRThreads"]), data =>
                        {
                            GlobalHost.ConnectionManager.GetHubContext<ListingHub>().Clients.Group(data.ListingID.ToString(CultureInfo.InvariantCulture)).updateListingAction(data);
                        });

                        _queueManager.OnListingStatusChange(Strings.QueueNames.ListingStatusUpdateForSignalR, int.Parse(ConfigurationManager.AppSettings["ListingStatusUpdateForSignalRThreads"]), data =>
                        {
                            GlobalHost.ConnectionManager.GetHubContext<ListingHub>().Clients.Group(data.ListingID.ToString(CultureInfo.InvariantCulture)).updateListingStatus(data);
                        });

                        _queueManager.OnEventStatusChange(Strings.QueueNames.EventStatusUpdateForSignalR, int.Parse(ConfigurationManager.AppSettings["EventStatusUpdateForSignalRThreads"]), data =>
                        {
                            GlobalHost.ConnectionManager.GetHubContext<ListingHub>().Clients.Group(data.EventID.ToString(CultureInfo.InvariantCulture)).updateEventStatus(data);
                        });

                        bool forceTest = bool.Parse(ConfigurationManager.AppSettings["ForceAsyncBidWaitForTesting"]);
                        _queueManager.OnListingActionResponse(Strings.QueueNames.ListingActionResponse, int.Parse(ConfigurationManager.AppSettings["ListingActionResponseForSignalRThreads"]), data =>
                        {
                            if (forceTest)
                                ((AutoResetEvent)
                                    Application[
                                        data.Action_UserName + data.Action_ListingID.ToString(CultureInfo.InvariantCulture)]).Set();
                            GlobalHost.ConnectionManager.GetHubContext<ListingHub>().Clients.Group(data.Action_UserName).listingActionResponse(data);
                        });

                        _queueManager.OnCurrentTimeUpdate(Strings.QueueNames.GetCurrentTimeForSignalR, int.Parse(ConfigurationManager.AppSettings["GetCurrentTimeForSignalRThreads"]), data =>
                        {
                            //data = data.AddHours(SiteClient.TimeZoneOffset);
                            TimeZoneInfo siteTimeZone = TimeZoneInfo.FindSystemTimeZoneById(SiteClient.SiteTimeZone);
                            data = TimeZoneInfo.ConvertTime(data, TimeZoneInfo.Utc, siteTimeZone);
                            GlobalHost.ConnectionManager.GetHubContext<ListingHub>().Clients.All.updateCurrentTime(data.ToString(CultureInfo.InvariantCulture));
                        });
                    }
                    else
                    {
                        bool forceTest = bool.Parse(ConfigurationManager.AppSettings["ForceAsyncBidWaitForTesting"]);
                        _queueManager.OnSignalRMessage(untypedData =>
                        {
                            switch (untypedData.MessageType)
                            {
                                case "ListingDTTMChange":
                                    {
                                        var data = (ListingDTTMChange)untypedData.MessageData;
                                        TimeZoneInfo siteTimeZone = TimeZoneInfo.FindSystemTimeZoneById(SiteClient.SiteTimeZone);
                                        data.DTTMString = TimeZoneInfo.ConvertTime(data.DTTM, TimeZoneInfo.Utc, siteTimeZone).ToString(CultureInfo.InvariantCulture);
                                        GlobalHost.ConnectionManager.GetHubContext<ListingHub>().Clients.Group(data.ListingID.ToString(CultureInfo.InvariantCulture)).updateListingDTTM(data);
                                        break;
                                    }
                                case "ListingActionChange":
                                    {
                                        var data = (ListingActionChange)untypedData.MessageData;
                                        GlobalHost.ConnectionManager.GetHubContext<ListingHub>().Clients.Group(data.ListingID.ToString(CultureInfo.InvariantCulture)).updateListingAction(data);
                                        break;
                                    }
                                case "ListingStatusChange":
                                    {
                                        var data = (ListingStatusChange)untypedData.MessageData;
                                        GlobalHost.ConnectionManager.GetHubContext<ListingHub>().Clients.Group(data.ListingID.ToString(CultureInfo.InvariantCulture)).updateListingStatus(data);
                                        break;
                                    }
                                case "EventStatusChange":
                                    {
                                        var data = (EventStatusChange)untypedData.MessageData;
                                        GlobalHost.ConnectionManager.GetHubContext<ListingHub>().Clients.Group(data.EventID.ToString(CultureInfo.InvariantCulture)).updateEventStatus(data);
                                        break;
                                    }
                                case "ListingActionResponse":
                                    {
                                        var data = (ListingActionResponse)untypedData.MessageData;
                                        if (forceTest)
                                            ((AutoResetEvent)
                                                Application[
                                                    data.Action_UserName + data.Action_ListingID.ToString(CultureInfo.InvariantCulture)]).Set();
                                        GlobalHost.ConnectionManager.GetHubContext<ListingHub>().Clients.Group(data.Action_UserName).listingActionResponse(data);
                                        break;
                                    }
                                case "CurrentTimeUpdate":
                                    {
                                        var data = (DateTime)untypedData.MessageData;
                                        TimeZoneInfo siteTimeZone = TimeZoneInfo.FindSystemTimeZoneById(SiteClient.SiteTimeZone);
                                        data = TimeZoneInfo.ConvertTime(data, TimeZoneInfo.Utc, siteTimeZone);
                                        GlobalHost.ConnectionManager.GetHubContext<ListingHub>().Clients.All.updateCurrentTime(data.ToString(CultureInfo.InvariantCulture));
                                        break;
                                    }
                                case "SaleInvoiceStatusChange":
                                    {
                                        var data = (SaleInvoiceStatusChange)untypedData.MessageData;
                                        GlobalHost.ConnectionManager.GetHubContext<ListingHub>().Clients.Group(data.ListingID.ToString(CultureInfo.InvariantCulture)).updateInvoiceStatus(data);
                                        break;
                                    }
                            }
                        });
                    }
                }
                else
                {
                    if (!_queueManager.IsSimpleQueueArchitecture())
                    {
                        _queueManager.OnListingDTTMChange(Strings.QueueNames.ListingDTTMUpdateForSignalR, int.Parse(ConfigurationManager.AppSettings["ListingDTTMUpdateForSignalRThreads"]), data =>
                        {
                            //data.DTTMString = data.DTTM.AddHours(SiteClient.TimeZoneOffset).ToString(CultureInfo.InvariantCulture);
                            TimeZoneInfo siteTimeZone = TimeZoneInfo.FindSystemTimeZoneById(SiteClient.SiteTimeZone);
                            data.DTTMString = TimeZoneInfo.ConvertTime(data.DTTM, TimeZoneInfo.Utc, siteTimeZone).ToString(CultureInfo.InvariantCulture);
                            GlobalHost.ConnectionManager.GetHubContext<ListingHub>().Clients.Group("AllListings").updateListingDTTM(data);
                        });

                        _queueManager.OnListingActionChange(Strings.QueueNames.ListingActionUpdateForSignalR, int.Parse(ConfigurationManager.AppSettings["ListingActionUpdateForSignalRThreads"]), data =>
                        {
                            GlobalHost.ConnectionManager.GetHubContext<ListingHub>().Clients.Group("AllListings").updateListingAction(data);
                        });

                        _queueManager.OnListingStatusChange(Strings.QueueNames.ListingStatusUpdateForSignalR, int.Parse(ConfigurationManager.AppSettings["ListingStatusUpdateForSignalRThreads"]), data =>
                        {
                            GlobalHost.ConnectionManager.GetHubContext<ListingHub>().Clients.Group("AllListings").updateListingStatus(data);
                        });

                        _queueManager.OnEventStatusChange(Strings.QueueNames.EventStatusUpdateForSignalR, int.Parse(ConfigurationManager.AppSettings["EventStatusUpdateForSignalRThreads"]), data =>
                        {
                            GlobalHost.ConnectionManager.GetHubContext<ListingHub>().Clients.Group(data.EventID.ToString(CultureInfo.InvariantCulture)).updateEventStatus(data);
                        });

                        bool forceTest = bool.Parse(ConfigurationManager.AppSettings["ForceAsyncBidWaitForTesting"]);
                        _queueManager.OnListingActionResponse(Strings.QueueNames.ListingActionResponse, int.Parse(ConfigurationManager.AppSettings["ListingActionResponseForSignalRThreads"]), data =>
                        {
                            if (forceTest)
                                ((AutoResetEvent)
                                    Application[
                                        data.Action_UserName + data.Action_ListingID.ToString(CultureInfo.InvariantCulture)]).Set();
                            GlobalHost.ConnectionManager.GetHubContext<ListingHub>().Clients.Group(data.Action_UserName).listingActionResponse(data);
                        });

                        _queueManager.OnCurrentTimeUpdate(Strings.QueueNames.GetCurrentTimeForSignalR, int.Parse(ConfigurationManager.AppSettings["GetCurrentTimeForSignalRThreads"]), data =>
                        {
                            //data = data.AddHours(SiteClient.TimeZoneOffset);
                            TimeZoneInfo siteTimeZone = TimeZoneInfo.FindSystemTimeZoneById(SiteClient.SiteTimeZone);
                            data = TimeZoneInfo.ConvertTime(data, TimeZoneInfo.Utc, siteTimeZone);
                            GlobalHost.ConnectionManager.GetHubContext<ListingHub>().Clients.All.updateCurrentTime(data.ToString(CultureInfo.InvariantCulture));
                        });
                    }
                    else
                    {
                        bool forceTest = bool.Parse(ConfigurationManager.AppSettings["ForceAsyncBidWaitForTesting"]);
                        _queueManager.OnSignalRMessage(untypedData =>
                        {
                            switch (untypedData.MessageType)
                            {
                                case "ListingDTTMChange":
                                    {
                                        var data = (ListingDTTMChange)untypedData.MessageData;
                                        TimeZoneInfo siteTimeZone = TimeZoneInfo.FindSystemTimeZoneById(SiteClient.SiteTimeZone);
                                        data.DTTMString = TimeZoneInfo.ConvertTime(data.DTTM, TimeZoneInfo.Utc, siteTimeZone).ToString(CultureInfo.InvariantCulture);
                                        GlobalHost.ConnectionManager.GetHubContext<ListingHub>().Clients.Group("AllListings").updateListingDTTM(data);
                                        break;
                                    }
                                case "ListingActionChange":
                                    {
                                        var data = (ListingActionChange)untypedData.MessageData;
                                        GlobalHost.ConnectionManager.GetHubContext<ListingHub>().Clients.Group("AllListings").updateListingAction(data);
                                        break;
                                    }
                                case "ListingStatusChange":
                                    {
                                        var data = (ListingStatusChange)untypedData.MessageData;
                                        GlobalHost.ConnectionManager.GetHubContext<ListingHub>().Clients.Group("AllListings").updateListingStatus(data);
                                        break;
                                    }
                                case "EventStatusChange":
                                    {
                                        var data = (EventStatusChange)untypedData.MessageData;
                                        GlobalHost.ConnectionManager.GetHubContext<ListingHub>().Clients.Group("AllListings").updateEventStatus(data);
                                        break;
                                    }
                                case "ListingActionResponse":
                                    {
                                        var data = (ListingActionResponse)untypedData.MessageData;
                                        if (forceTest)
                                            ((AutoResetEvent)
                                                Application[
                                                    data.Action_UserName + data.Action_ListingID.ToString(CultureInfo.InvariantCulture)]).Set();
                                        GlobalHost.ConnectionManager.GetHubContext<ListingHub>().Clients.Group(data.Action_UserName).listingActionResponse(data);
                                        break;
                                    }
                                case "CurrentTimeUpdate":
                                    {
                                        var data = (DateTime)untypedData.MessageData;
                                        TimeZoneInfo siteTimeZone = TimeZoneInfo.FindSystemTimeZoneById(SiteClient.SiteTimeZone);
                                        data = TimeZoneInfo.ConvertTime(data, TimeZoneInfo.Utc, siteTimeZone);
                                        GlobalHost.ConnectionManager.GetHubContext<ListingHub>().Clients.All.updateCurrentTime(data.ToString(CultureInfo.InvariantCulture));
                                        break;
                                    }
                                case "SaleInvoiceStatusChange":
                                    {
                                        var data = (SaleInvoiceStatusChange)untypedData.MessageData;
                                        GlobalHost.ConnectionManager.GetHubContext<ListingHub>().Clients.Group("AllListings").updateInvoiceStatus(data);
                                        break;
                                    }
                            }
                        });
                    }
                }
            }

            try
            {
                //perform all AutoMapper mappings here
                Mapper.CreateMap<DTO.Event, APIEvent>();
                Mapper.CreateMap<DTO.User, APIUser>();
                Mapper.CreateMap<DTO.Listing, APIListing>()
                    .ForMember(d => d.WinningUser,
                        o => o.MapFrom(s => s.CurrentListingAction != null ? s.CurrentListingAction.UserName : string.Empty))
                    .ForMember(d => d.Decorations, o => o.MapFrom(s => s.Decorations.Select(x => x.Name).ToList()))
                    .ForMember(d => d.ActionCount,
                        o =>
                            o.MapFrom(s => s.AcceptedActionCount))
                    .ForMember(d => d.Locations, o => o.MapFrom(s => s.Locations.Select(x => x.Name).ToList()))
                    .ForMember(d => d.ReserveMet, o => o.ResolveUsing<APIListing.ReserveMetResolver>())
                    .ForMember(d => d.ImageURI, o => o.Ignore())
                    .ForMember(d => d.LotNumber, o => o.MapFrom(s => s.Lot != null ? s.Lot.LotNumber : null))
                    .ForMember(d => d.EventID, o => o.MapFrom(s => s.Lot != null ? (int?)s.Lot.Event.ID : null));
                Mapper.AssertConfigurationIsValid();
            }
            catch (Exception e)
            {
                LogManager.WriteLog(null, "AutoMapper Configuration Error in Application_Start()", "global.asax.cs", TraceEventType.Error, null, e);
            }

            stopwatch.Stop();
            LogManager.WriteLog("App init completed", "Application_Start()", "global.asax.cs", TraceEventType.Start, null, null,
                new Dictionary<string, object>() { { "Startup Time (MS)", stopwatch.ElapsedMilliseconds } });


            var myTimer = new System.Timers.Timer();
            // Tell the timer what to do when it elapses
            myTimer.Elapsed += async (sender, e) => await CronJob();
            // Set it to go off every 1 hour
            myTimer.Interval = 1000 * 60 * 60 * 1;
            //myTimer.Interval = 300000;
            // And start it        
            myTimer.Enabled = true;

            // CheckDuplicateAddresses();

            // commissionReport();

            // FillSellerPaypalFees();

            // paypalDispursementCronJob();

            // uploadListingsToGoogle(true);

            // emailReminderForPaidNotShippedInvoices();

            // CronJob();

            // Fetch all the users data along with their primary addresses and convert into a csv format
            // Primary Address -> UserClient.GetAddresses("admin", users[0].UserName).Find(address => address.ID == users[0].PrimaryAddressID);

            Debug.Write(GenerateVendorReport());

            // Generate custom invoice report
            Debug.Write(GenerateCustomInvoiceReport());

            Debug.WriteLine("Application Started");
        }
        
        // Shopify User Import
        // First Name	Last Name	Email	Accepts Email Marketing	Default Address Company	Default Address Address1	Default Address Address2	Default Address City	Default Address Province Code	Default Address Country Code	Default Address Zip	Default Address Phone	Phone	Accepts SMS Marketing	Tags	Note	Tax Exempt

        public class ShopifyUserImportRecord
        {
            [Name("First Name")]
            public string FirstName { get; set; }
            
            [Name("Last Name")]
            public string LastName { get; set; }
            
            [Name("Email")]
            public string Email { get; set; }

            [Name("Accepts Email Marketing")]
            public string AcceptsEmailMarketing { get; set; }

            [Name("Default Address Company")]
            public string DefaultAddressCompany { get; set; }

            [Name("Default Address Address1")]
            public string DefaultAddressAddress1 { get; set; }

            [Name("Default Address Address2")]
            public string DefaultAddressAddress2 { get; set; }

            [Name("Default Address City")]
            public string DefaultAddressCity { get; set; }

            [Name("Default Address Province Code")]
            public string DefaultAddressProvinceCode { get; set; }

            [Name("Default Address Country Code")]
            public string DefaultAddressCountryCode { get; set; }

            [Name("Default Address Zip")]
            public string DefaultAddressZip { get; set; }

            [Name("Default Address Phone")]
            public string DefaultAddressPhone { get; set; }

            [Name("Phone")]
            public string Phone { get; set; }

            [Name("Accepts SMS Marketing")]
            public string AcceptsSMSMarketing { get; set; }

            [Name("Tags")]
            public string Tags { get; set; }

            [Name("Note")]
            public string Note { get; set; }

            [Name("Tax Exempt")]
            public string TaxExempt { get; set; }
        }

        public class VendorCsvRecord
        {
            [Name("ST Unique ID")]
            public int STUniqueID { get; set; }

            [Name("Title(Mandatory)")]
            public string Title { get; set; }

            [Name("Company name")]
            public string CompanyName { get; set; }

            [Name("Brand name")]
            public string BrandName { get; set; }

            [Name("Contact name")]
            public string ContactName { get; set; }

            [Name("Phone number")]
            public string PhoneNumber { get; set; }

            [Name("Email")]
            public string Email { get; set; }

            [Name("Address 1")]
            public string Address1 { get; set; }

            [Name("Address 2")]
            public string Address2 { get; set; }

            [Name("Pin code")]
            public string PinCode { get; set; }

            [Name("City")]
            public string City { get; set; }

            [Name("State")]
            public string State { get; set; }

            [Name("Country")]
            public string Country { get; set; }

            [Name("Website")]
            public string Website { get; set; }

            [Name("Type of vendor(Dropshipper/Marketplace)")]
            public string TypeOfVendor { get; set; }

            [Name("Is Gst enabled")]
            public string IsGstEnabled { get; set; }

            [Name("Tax number/GSTIN")]
            public string TaxNumberGSTIN { get; set; }

            [Name("Billing Address same as Company details")]
            public string BillingAddressSameAsCompanyDetails { get; set; }

            [Name("Billing contact name")]
            public string BillingContactName { get; set; }

            [Name("Billing phone")]
            public string BillingPhone { get; set; }

            [Name("Billing address line 1")]
            public string BillingAddressLine1 { get; set; }

            [Name("Billing address line 2")]
            public string BillingAddressLine2 { get; set; }

            [Name("Billing city")]
            public string BillingCity { get; set; }

            [Name("Billing Pin code")]
            public string BillingPinCode { get; set; }

            [Name("Billing State")]
            public string BillingState { get; set; }

            [Name("Billing Country")]
            public string BillingCountry { get; set; }

            [Name("Invoice prefix")]
            public string InvoicePrefix { get; set; }

            [Name("Invoice message 1")]
            public string InvoiceMessage1 { get; set; }

            [Name("Invoice message 2")]
            public string InvoiceMessage2 { get; set; }

            [Name("Vendor Connected Shop")]
            public string VendorConnectedShop { get; set; }

            [Name("image")]
            public string Image { get; set; }

            [Name("Commission Permission")]
            public string CommissionPermission { get; set; }

            [Name("Commission% Variable")]
            public string CommissionPercentVariable { get; set; }

            [Name("Commission% Fixed")]
            public string CommissionPercentFixed { get; set; }

            [Name("Product count")]
            public string ProductCount { get; set; }

            [Name("All Time Sales")]
            public string AllTimeSales { get; set; }

            [Name("All Time Vendor Earnings")]
            public string AllTimeVendorEarnings { get; set; }

            [Name("Vendor Payout Pending")]
            public string VendorPayoutPending { get; set; }

            [Name("Vendor Status")]
            public string VendorStatus { get; set; }
        }

        public class WarehouseCsvRecord
        {
            [Name("ST Unique ID")]
            public int STUniqueID { get; set; }

            [Name("Company/Vendor title (Mandatory)")]
            public string CompanyVendorTitle { get; set; }

            [Name("Keep details same as company/vendor details")]
            public string KeepDetailsSameAsCompany { get; set; }

            [Name("Warehouse Nick Name")]
            public string WarehouseNickName { get; set; }

            [Name("Contact Name")]
            public string ContactName { get; set; }

            [Name("Phone")]
            public string Phone { get; set; }

            [Name("Email")]
            public string Email { get; set; }

            [Name("Address Line 1")]
            public string AddressLine1 { get; set; }

            [Name("Address Line 2")]
            public string AddressLine2 { get; set; }

            [Name("City")]
            public string City { get; set; }

            [Name("Zip/Pincode")]
            public string ZipPincode { get; set; }

            [Name("State")]
            public string State { get; set; }

            [Name("Country")]
            public string Country { get; set; }

            [Name("Is Default")]
            public string IsDefault { get; set; }
        }

        // Product Import

        // Handle Title   Body(HTML) Vendor Type    Tags Published   Option1 Name    Option1 Value   Option2 Name    Option2 Value   Option3 Name    Option3 Value   Variant SKU Variant Grams   Variant Inventory Qty Track Inventory Variant Inventory Policy    Variant Price   Variant Compare At Price    Variant Requires Shipping Variant Barcode Image Src Variant Weight Unit Cost per item hsn tax length  width height  commission fixed commission Product subtitle / sp_683701bdc902b Where did you purchase this item from / sp_6882595786406    Condition / sp_68825a437e1e7    How Long Have You Had This Item / sp_68825b0b035ca  Has This Item Been Altered / sp_68825b9b501de   Measurements / sp_68825dec7b313
        //COMPULSARY COLUMNS - Don't leave blank		
        //------------------------------------------------------		
        //Handle*		"Handles are unique names for each product. They can contain letters, dashes, and numbers, but no spaces, accents, or other characters, including periods. Handles are used in the URL for each product.
        //For example, the handle for a ""Women's Snowboard"" should be womens-snowboard, and the product's URL would be https://yourstore.myshopify.com/products/womens-snowboard.
        //Every line in the CSV file that starts with a different handle is treated as a new product.To add multiple images to a product, you must add multiple lines with the same handle."
        //Title* The title of your product.For example, Women's Snowboard.


        //OTHER COLUMNS		
        //------------------------------------------------------		
        //Body (HTML) The description of the product in HTML format.
        //Vendor      "The name of the vendor for your product. For example, John's Apparel.
        //Values must be at least two characters long."
        //Type A custom label that describes the category of a product.Valid values are those which belong to the Category masters.
        //Tags A comma-separated list of tags used to tag the product.Most spreadsheet applications automatically add quotes around the tags for you.If you use a plain text editor, then you need to manually add the quotes. For example, “tag1, tag2, tag3”
        //Published Indicates whether a product is published on your online store.Valid values are TRUE if the product is published on your online store, or FALSE if the product is not available on your online store.
        //Option1 Name        If a product has an option, then enter its name. For example, Color.&#10;If a product has only one option, then this value should be Title.
        //Option1 Value		"Caution: Changing data in this column deletes existing variant IDs' values, and creates new variant IDs. Any change to variant ID values can break third-party dependencies on variant IDs.
        //If a product has an option, then enter its value. For example, Black.
        //If a product has only one option, then this value should be Default Title"
        //Option2 Name        If a product has a second option, then enter its name. For example, Size.
        //Option2 Value		"Caution: Changing data in this column deletes existing variant IDs values, and creates new variant IDs. Any change to variant ID values can break third-party dependencies on variant Ids.
        //If a product has a second option, then enter its value. For example, Large."
        //Option3 Name        If a product has a third option, then enter its name.
        //Option3 Value       Caution: Changing data in this column deletes existing variant IDs values, and creates new variant IDs.Any change to variant ID values can break third-party dependencies on variant IDs.If a product has a third option, then enter its value.
        //Variant SKU		"The SKU of the product or variant. This is used to track inventory with inventory tracking services.
        //This field can't be left blank if you're using a custom fulfillment service."
        //Variant Grams   Numeric	"The weight of the product or variant in grams. Don't specify a unit of measurement or use decimals. For example, for a weight of 5.125 kg, enter 5125.
        //We always import and export the weight in grams, even if you specify a different unit.You must use accurate weights if you intend to offer carrier-calculated shipping or use a third-party fulfillment service"
        //Variant Inventory Qty Numeric The number of items you have in stock of this product or variant.
        //Track Inventory     Boolean "Include your inventory tracking for this variant or product. Valid values include shopify, shipwire, amazon_marketplace_web, or blank if inventory isn't tracked.
        //If the existing inventory tracking options are removed, then inventory is no longer tracked.
        //Valid values are TRUE or FALSE."
        //Variant Inventory Policy How to handle orders when the inventory level for this product or variant has reached zero. Variants with a deny inventory policy can't be purchased after their inventory level reaches zero. Variants with a continue inventory policy can be purchased after their inventory level reaches zero, allowing for negative inventory levels.
        //Variant Price   Numeric The price of the product or variant. Only include the price and don't include any currency symbols. For example, 9.99
        //Variant Compare At Price    Numeric The "Compare at Price" of the product or variant.Only include the price and don't include any currency symbols. For example, 9.99.
        //Variant Requires Shipping Boolean The option to require shipping.Valid values are TRUE or FALSE.
        //Variant Barcode     The barcode, ISBN, or UPC of the product.
        //Image Src       Enter the URL for the product image.The URL must be a public URL.The app downloads the images during the import and re-uploads them into your store.These images aren&apos;t variant-specific.
        //Variant Weight Unit Valid values are g, kg, lb, and oz.
        //Cost per item   Numeric How much it costs you for the product or variant. Only include the cost. Don't include any currency symbols. For example, 9.99.
        //hsn Numeric The HSN code of the product. This must be part of the App's Tax Masters.
        //tax Numeric Only 2 decimal places allowed.Input either as 5.60% or 0.0560 (any digits after this will not be considered)
        //length Numeric Length of the Product when packaged.Do not enter unit.If 10 cm only enter 10
        //width Numeric Width of the Product when packaged. Do not enter unit. If 10 cm only enter 10
        //height Numeric Height of the Product when packaged. Do not enter unit. If 10 cm only enter 10
        //commission Numeric Variable commission for the Product. Only 2 decimal places allowed.Input either as 5.60% or 0.0560 (any digits after this will not be considered)
        //fixed commission Numeric Fixed commission for the Product.Only 2 decimal places allowed.Input as 5.60(any digits after this will not be considered)

        // Product Import Code
        public class ProductImportRecord
        {
            [Name("Handle")]
            public string Handle { get; set; }

            [Name("Title")]
            public string Title { get; set; }

            [Name("Body (HTML)")]
            public string BodyHtml { get; set; }

            [Name("Vendor")]
            public string Vendor { get; set; }

            [Name("Type")]
            public string Type { get; set; }
            
            [Name("Tags")]
            // Can be 4 values: mp closet, prop closet, robins closet, mp exclusive
            public string Tags { get; set; }

            [Name("Published")]
            public string Published { get; set; }   

            [Name("Option1 Name")]
            public string Option1Name { get; set; }

            [Name("Option1 Value")]
            public string Option1Value { get; set; }

            [Name("Option2 Name")]
            public string Option2Name { get; set; }

            [Name("Option2 Value")]
            public string Option2Value { get; set; }

            [Name("Option3 Name")]
            public string Option3Name { get; set; }

            [Name("Option3 Value")]
            public string Option3Value { get; set; }

            [Name("Variant SKU")]
            public string VariantSKU { get; set; }

            [Name("Variant Grams")]
            public string VariantGrams { get; set; }

            [Name("Variant Inventory Qty")]
            public string VariantInventoryQty { get; set; }

            [Name("Track Inventory")]
            public string TrackInventory { get; set; }

            [Name("Variant Inventory Policy")]
            public string VariantInventoryPolicy { get; set; }

            [Name("Variant Price")]
            public string VariantPrice { get; set; }

            [Name("Variant Compare At Price")]
            public string VariantCompareAtPrice { get; set; }

            [Name("Variant Requires Shipping")]
            public string VariantRequiresShipping { get; set; }

            [Name("Variant Barcode")]
            public string VariantBarcode { get; set; }

            [Name("Image Src")]
            public string ImageSrc { get; set; }

            [Name("Variant Weight Unit")]
            public string VariantWeightUnit { get; set; }
            
            [Name("Cost per item")]
            public string CostPerItem { get; set; }

            [Name("hsn")]
            public string Hsn { get; set; }

            [Name("tax")]
            public string Tax { get; set; }

            [Name("length")]
            public string Length { get; set; }

            [Name("width")]
            public string Width { get; set; }

            [Name("height")]
            public string Height { get; set; }

            [Name("commission")]
            public string Commission { get; set; }

            [Name("fixed commission")]
            public string FixedCommission { get; set; }

            [Name("Product subtitle / sp_683701bdc902b")]
            public string ProductSubtitle { get; set; }

            [Name("Where did you purchase this item from / sp_6882595786406")]
            public string WhereDidYouPurchaseThisItemFrom { get; set; }

            [Name("Condition / sp_68825a437e1e7")]
            public string Condition { get; set; }

            [Name("How Long Have You Had This Item / sp_68825b0b035ca")]
            public string HowLongHaveYouHadThisItem { get; set; }

            [Name("Has This Item Been Altered / sp_68825b9b501de")]
            public string HasThisItemBeenAltered { get; set; }

            [Name("Measurements / sp_68825dec7b313")]
            public string Measurements { get; set; }
        }

        public (string VendorReport, string ShopifyUserImportReport, string WarehouseReport) GenerateVendorReport()
        {
            var users = UserClient.GetAllUsers("admin", 0, 10000, "id", false).List;
            var vendorRecords = new List<VendorCsvRecord>();
            var shopifyUserImportRecords = new List<ShopifyUserImportRecord>();
            var warehouseRecords = new List<WarehouseCsvRecord>();
            var productRecords = new List<ProductImportRecord>();

            foreach (var userObj in users.Select((u, index) => new { User = u, Index = index }))
            {
                var addresses = UserClient.GetAddresses("admin", userObj.User.UserName).ToList();
                var primaryAddress = addresses.FirstOrDefault(a => a.ID == userObj.User.PrimaryAddressID);

                if (primaryAddress == null) continue;

                vendorRecords.Add(new VendorCsvRecord
                {
                    STUniqueID = userObj.Index + 1,
                    Title = $"{primaryAddress.FirstName} {primaryAddress.LastName}".Trim(),
                    CompanyName = userObj.User.UserName,
                    ContactName = $"{primaryAddress.FirstName} {primaryAddress.LastName}".Trim(),
                    PhoneNumber = userObj.User.PhoneNumber,
                    Email = userObj.User.Email,
                    Address1 = primaryAddress.Street1,
                    Address2 = primaryAddress.Street2,
                    PinCode = primaryAddress.ZipPostal,
                    City = primaryAddress.City,
                    State = primaryAddress.StateRegion,
                    Country = primaryAddress.Country?.Name ?? "",
                    TypeOfVendor = "Dropshipper",
                    IsGstEnabled = "NO",
                    BillingAddressSameAsCompanyDetails = "YES",
                    BillingContactName = $"{primaryAddress.FirstName} {primaryAddress.LastName}".Trim(),
                    BillingPhone = userObj.User.PhoneNumber,
                    BillingAddressLine1 = primaryAddress.Street1,
                    BillingAddressLine2 = primaryAddress.Street2,
                    BillingCity = primaryAddress.City,
                    BillingPinCode = primaryAddress.ZipPostal,
                    BillingState = primaryAddress.StateRegion,
                    BillingCountry = primaryAddress.Country?.Name ?? "",
                    VendorStatus = "Active",
                    // Empty fields
                    BrandName = "",
                    Website = "",
                    TaxNumberGSTIN = "",
                    InvoicePrefix = "",
                    InvoiceMessage1 = "",
                    InvoiceMessage2 = "",
                    VendorConnectedShop = "",
                    Image = "",
                    CommissionPermission = "",
                    CommissionPercentVariable = "",
                    CommissionPercentFixed = "",
                    ProductCount = "",
                    AllTimeSales = "",
                    AllTimeVendorEarnings = "",
                    VendorPayoutPending = ""
                });

                shopifyUserImportRecords.Add(new ShopifyUserImportRecord
                {
                    FirstName = primaryAddress.FirstName,
                    LastName = primaryAddress.LastName,
                    Email = userObj.User.Email,
                    AcceptsEmailMarketing = "TRUE",
                    DefaultAddressCompany = userObj.User.UserName,
                    DefaultAddressAddress1 = primaryAddress.Street1,
                    DefaultAddressAddress2 = primaryAddress.Street2,
                    DefaultAddressCity = primaryAddress.City,
                    DefaultAddressProvinceCode = primaryAddress.StateRegion,
                    DefaultAddressCountryCode = primaryAddress.Country?.Name ?? "",
                    DefaultAddressZip = primaryAddress.ZipPostal,
                    DefaultAddressPhone = userObj.User.PhoneNumber,
                    Phone = userObj.User.PhoneNumber,
                    AcceptsSMSMarketing = "TRUE",
                    Tags = "",
                    Note = "",
                    TaxExempt = "FALSE"
                });

                // Warehouse Records
                foreach (var address in addresses)
                {
                    bool isPrimary = address.ID == userObj.User.PrimaryAddressID;
                    warehouseRecords.Add(new WarehouseCsvRecord
                    {
                        STUniqueID = userObj.Index + 1,
                        CompanyVendorTitle = userObj.User.UserName,
                        KeepDetailsSameAsCompany = isPrimary ? "YES" : "NO",
                        WarehouseNickName = "N/A",
                        ContactName = isPrimary ? userObj.User.UserName
                            : $"{address.FirstName} {address.LastName}".Trim(),
                        Phone = userObj.User.PhoneNumber,
                        Email = userObj.User.Email,
                        AddressLine1 = address.Street1,
                        AddressLine2 = address.Street2,
                        City = address.City,
                        ZipPincode = address.ZipPostal,
                        State = address.StateRegion,
                        Country = address.Country?.Name ?? "",
                        IsDefault = isPrimary.ToString().ToUpper() 
                    });
                }

                var userListings = ListingClient.GetEndedListingsByOwnerWithFillLevel("admin", userObj.User.UserName, 0, 1000, null, false, ListingFillLevels.All).List;
                var tags = new List<string>();
                foreach (var listing in userListings) {
                    if (listing == null) continue;
                    if (listing.Owner.Email.ToLower().Equals("<EMAIL>")) {
                        tags.Add("robins closet");
                    } else if (listing.Owner.Email.ToLower().Equals("<EMAIL>")) {
                        tags.Add("prop closet");
                    } else if (listing.Owner.Email.ToLower().Equals("<EMAIL>")) {
                        tags.Add("mp closet");
                    } else if (listing.Owner.Email.ToLower().Equals("<EMAIL>")) {
                        tags.Add("mp exclusive");
                    }
                    productRecords.Add(new ProductImportRecord
                    {
                        Handle = listing.ID.ToString(),
                        Title = listing.Title,
                        BodyHtml = listing.Description,
                        Vendor = userObj.User.UserName,
                        Type = listing.PrimaryCategory.Name,
                        Tags = string.Join(",", tags),
                        Images = string.Join(",", listing.ListingVariants.Select(v => v.ImageURL)),
                        Measurements = string.Join(",", listing.ListingOptions.Select(o => $"{o.OptionName}: {o.OptionValue}")),
                        Price = listing.ListingVariants.FirstOrDefault()?.Price.ToString() ?? "0",
                        CompareAtPrice = listing.ListingVariants.FirstOrDefault()?.CompareAtPrice.ToString() ?? "0",
                        Weight = listing.ListingVariants.FirstOrDefault()?.Weight.ToString() ?? "0",
                        WeightUnit = listing.ListingVariants.FirstOrDefault()?.WeightUnit?.Name ?? "",
                        InventoryQuantity = listing.ListingVariants.FirstOrDefault()?.InventoryQuantity.ToString() ?? "0",
                        InventoryPolicy = "deny",
                        FulfillmentService = "manual",
                        RequiresShipping = "true",
                        Price = listing.ListingVariants.FirstOrDefault()?.Price.ToString() ?? "0"




                    });
                }


            }

            //var productRecords = new List<ProductImportRecord>();
            //var context = new aweFramework();

            //var listings = context.RWX_Listings
            //    .Include(l => l.Vendor)
            //    .Include(l => l.ListingOptions)
            //    .Include(l => l.ListingVariants)
            //    .Where(l => l.Vendor != null && !string.IsNullOrEmpty(l.Vendor.UserName))
            //    .ToList();

            var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
            var vendorReportName = $"Vendors_{timestamp}.csv";
            var shopifyUserImportReportName = $"ShopifyUserImport_{timestamp}.csv";
            var warehouseReportName = $"Warehouses_{timestamp}.csv";
            var folder = "~/App_Data/Reports";
            var vendorPath = HostingEnvironment.MapPath(Path.Combine(folder, vendorReportName));
            Directory.CreateDirectory(Path.GetDirectoryName(vendorPath));
            using (var writer = new StreamWriter(vendorPath))
            using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
            {
                csv.WriteRecords(vendorRecords);
            }

            var shopifyUserImportPath = HostingEnvironment.MapPath(Path.Combine(folder, shopifyUserImportReportName));
            Directory.CreateDirectory(Path.GetDirectoryName(shopifyUserImportPath));
            using (var writer = new StreamWriter(shopifyUserImportPath))
            using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
            {
                csv.WriteRecords(shopifyUserImportRecords);
            }

            var warehousePath = HostingEnvironment.MapPath(Path.Combine(folder, warehouseReportName));
            using (var writer = new StreamWriter(warehousePath))
            using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
            {
                csv.WriteRecords(warehouseRecords);
            }

            return (vendorReportName, shopifyUserImportReportName, warehouseReportName);
        }

        protected void Application_End()
        {
            //If FrameWorx is running InProcess (In IIS, by itself...)
            if (Clients.ClientFactory.ExecutionContext == Clients.ClientFactory.ExecutionContextEnum.InProcess)
            {
                //application is ending, no reason to freak out if these things are already null
                if (_notifierService != null)
                {
                    _notifierService.StopThreads();
                }
                if (_schedulerService != null)
                {
                    _schedulerService.StopThreads();
                }
            }
        }

        private Task CronJob()
        {
            if (
                (DateTime.UtcNow.Hour > 5 && DateTime.UtcNow.Hour <= 6) ||
                (DateTime.UtcNow.Hour > 11 && DateTime.UtcNow.Hour <= 12) ||
                (DateTime.UtcNow.Hour > 17 && DateTime.UtcNow.Hour <= 18) ||
                (DateTime.UtcNow.Hour > 23 && DateTime.UtcNow.Hour <= 24)
              )
            {
                Debug.WriteLine("Running Cron job..............");

                LogManager.WriteLog("Cron Job Started at " + DateTime.Now.ToString(), "Global.paypalDispursementCronJob", "global.asax.cs"
                            , TraceEventType.Information, null, null, new Dictionary<string, object>() { });
                aweFramework context = new aweFramework();
                // Pull shipped orders and 


                List<RWX_PaymentResponses> shippedOrders = context.RWX_PaymentResponses
                            .Where(p1 => p1.Status != "Dispursed" &&
                                !context.RWX_PaymentResponses
                                    .Where(p2 => p1.InvoiceId == p2.InvoiceId)
                                    .GroupBy(p2 => p2.InvoiceId)
                                    .Any(g => g.Select(p2 => p2.Status).Count() > 1) &&
                                !context.Refunds.Any(r => p1.InvoiceId == r.invoice_id))
                            .ToList();

                //List<RWX_PaymentResponses> shippedorderes = context.RWX_PaymentResponses.Where(p => p.InvoiceId == 239773).ToList();
                Debug.WriteLine("Context created..............");
                Debug.WriteLine("Total Records to process: " + shippedOrders.Count);
                Debug.WriteLine("Total Records to process: " + shippedOrders.Count);
                Debug.WriteLine("Total Records to process: " + shippedOrders.Count);
                Debug.WriteLine("Total Records to process: " + shippedOrders.Count);
                LogManager.WriteLog("Cron Job Progress: Total Invoice count: " + shippedOrders.Count(), "Global.paypalDispursementCronJob", "global.asax.cs"
                            , TraceEventType.Information, null, null, new Dictionary<string, object>() { });
                List<RWX_PaymentResponses> completedShipments = getCompletedShipments(shippedOrders);
                LogManager.WriteLog("Cron Job Progress: Total shipped count: " + completedShipments.Count(), "Global.paypalDispursementCronJob", "global.asax.cs"
                            , TraceEventType.Information, null, null, new Dictionary<string, object>() { });
                List<RWX_PaymentResponses> paidOrders = dispursePaypalPayment(completedShipments);

                LogManager.WriteLog("Cron Job Progress: Total disbursed count: " + paidOrders.Count(), "Global.paypalDispursementCronJob", "global.asax.cs"
                            , TraceEventType.Information, null, null, new Dictionary<string, object>() { });
                context.SaveChangesAsync();
                Debug.WriteLine("Completed Task..............");

                emailReminderForPaidNotShippedInvoices();

                // Pull data which is initiated and auto approve after 3 days
                // Pull data which is in transit and check for delivery and release payment
                aweFramework refundContext = new aweFramework();
                List<Refunds> refundList = refundContext.Refunds.Where(r => r.refund_status != "COMPLETED" && r.refund_status != "CANCELLED").ToList();
                LogManager.WriteLog("Cron Job Progress: Total shipped count: " + completedShipments.Count(), "Global.paypalDispursementCronJob", "global.asax.cs"
                            , TraceEventType.Information, null, null, new Dictionary<string, object>() { });
                List<Refunds> refundListUpdated = processRefunds(refundList);
                refundContext.SaveChangesAsync();
                LogManager.WriteLog("Cron Job Completed at " + DateTime.Now.ToString(), "Global.paypalDispursementCronJob", "global.asax.cs"
                            , TraceEventType.Information, null, null, new Dictionary<string, object>() { });


            }
            else if (DateTime.UtcNow.Hour > 8 && DateTime.UtcNow.Hour <= 9)
            {
                Debug.WriteLine("Running Cron job for Google Shopping..............");
                uploadListingsToGoogle(false);
                Debug.WriteLine("Completed Task..............");
            }
            else if (DateTime.UtcNow.Hour > 13 && DateTime.UtcNow.Hour <= 14)
            {
                if (DateTime.UtcNow.Day == 3)
                {
                    commissionReport();
                }
                if (DateTime.UtcNow.DayOfWeek == DayOfWeek.Saturday)
                {
                    CheckDuplicateAddresses();
                }
                FillSellerPaypalFeesDaily();
                SendRatingReminder();
            }
            else if (DateTime.UtcNow.Hour > 1 && DateTime.UtcNow.Hour <= 2)
            {
                Debug.WriteLine("Running Cron job for Emailing UnPaid Invoice Reminders");

                aweFramework context = new aweFramework();
                List<RWX_Invoices> unpaidListings = context.RWX_Invoices
                    .Where(inv => inv.Status == "New" && inv.DeletedOn == null)
                    .ToList();

                foreach (var inv in unpaidListings)
                {
                    TimeSpan timeSinceCreated = DateTime.Now.Subtract(inv.CreatedDTTM.Value);
                    double hoursSinceCreated = timeSinceCreated.TotalHours;

                    if (hoursSinceCreated >= 72 && hoursSinceCreated < 84)
                    {
                        SendInvoiceReminderNotification(inv, MagnoliapearlStringConstants.CronJob_UnpaidInvoice_Seller_Email, MagnoliapearlStringConstants.CronJob_UnPaidInvoiceReminder2_Buyer_Email);
                    }
                    else if (hoursSinceCreated >= 24 && hoursSinceCreated < 36)
                    {
                        SendInvoiceReminderNotification(inv, null, MagnoliapearlStringConstants.CronJob_UnPaidInvoiceReminder1_Buyer_Email);
                    }
                }

                Debug.WriteLine("Completed Task..............");
            }
            return Task.CompletedTask;
        }

        #region Refunds

        private List<Refunds> processRefunds(List<Refunds> refundList)
        {
            aweFramework context = new aweFramework();
            string upsAccessToken = getUPSAccessToken();
            var paypal_access_token = getPaypalAccessToken();

            refundList.ForEach(refund =>
            {
                if (refund.refund_status == "INITIATED")
                {
                    var refund_date = refund.update_dttm;
                    if (refund_date == null)
                    {
                        refund_date = refund.create_dttm;
                    }
                    if ((DateTime.Now - refund_date.Value).TotalDays > 3)
                    {
                        // Auto Approved refund request.
                        // Generate return label here
                        string order_id = "";
                        string shipment_id = "";
                        (order_id, shipment_id, refund.tracking_number, refund.shipping_label) = createShipStationReturnOrder(refund.invoice_id.Value, true);
                        refund.auto_approved = true;
                        refund.shipping_order_id = order_id + ";" + shipment_id;
                        refund.refund_status = "IN-TRANSIT";
                    }
                }
                else if (refund.refund_status == "IN-TRANSIT")
                {
                    // Check if package delivered. If delivered, refund amount to buyer
                    if (refund.tracking_number != null && !refund.tracking_number.Trim().Equals(""))
                    {
                        bool isDelivered = checkIfDelivered(upsAccessToken, new List<string> { refund.tracking_number.ToString() });
                        if (isDelivered)
                        {
                            Invoice invoice = AccountingClient.GetInvoiceByID("admin", refund.invoice_id.Value);

                            RWX_PaymentResponses paymentResponses = context.RWX_PaymentResponses.FirstOrDefault(x => x.InvoiceId == invoice.ID);
                            invoice.PaymentHistory.ForEach(payment =>
                            {
                                string avalaraId = "";
                                var extras = paymentResponses.AllFields;
                                var splittedData = (extras != null) ? extras.Split(';') : new string[] { };
                                var avaTransactionCode = "";
                                for (var i = 0; i < splittedData.Length; i++)
                                {
                                    var isplit = splittedData[i].Split(':');
                                    if (isplit.Length > 1)
                                    {
                                        if (isplit[0].Equals("AvalaraCode"))
                                        {
                                            avaTransactionCode = isplit[1];
                                        }
                                    }
                                }

                                string transactionId = paypalIssueCompleteRefund(paypal_access_token, invoice.Owner.PayPalEmail(), payment.TransactionId);
                                string AvataxFields = completeRefundAvalara(avaTransactionCode);
                                int responseId = markInvoiceUnpaid(invoice, transactionId, AvataxFields);
                                if (responseId != -10)
                                {
                                    refund.payment_response_id = responseId;
                                    refund.refund_status = "COMPLETED";
                                }
                                // Create site fees for shipping and return shipping label
                                //Invoice invoice = AccountingClient.GetInvoiceByID("admin", refund.invoice_id.Value);

                                IDataContext data = UnityResolver.Get<IDataContext>();
                                invoice.LineItems = AccountingClient.GetLineItemsByInvoice("admin", invoice.ID, 0,
                                                                                 0, Strings.Fields.DateStamp, false).List;
                                DTO.LineItem lineItem = new DTO.LineItem
                                {
                                    TotalAmount = (invoice.ShippingAmount + invoice.BuyersPremiumAmount) * 2,
                                    Quantity = 0,
                                    PerUnitAmount = 0.0M,
                                    Currency = Cache.SiteProperties[Strings.SiteProperties.SiteCurrency],
                                    DateStamp = DateTime.UtcNow,
                                    Properties = new Dictionary<string, string>(),
                                    Description = "Shipping Fees for Invoice#" + invoice.ID,
                                    Owner = data.GetSiteOwner(),
                                    Payer = invoice.Owner,
                                    ListingID = invoice.LineItems[0].ListingID,
                                    Type = Strings.LineItemTypes.Fee,
                                    Status = Strings.LineItemStatuses.Pending,
                                    BuyersPremiumApplies = false,
                                    IsArchived = false

                                };
                                data.AddLineItem("admin", lineItem);

                                Invoice inv = AccountingClient.CreateInvoiceFromLineItem("admin", lineItem.ID);
                            });
                        }
                        else
                        {

                            // Check for date and cancel Refund if not delivered for 10 days.
                            // Dispurse amount to buyer
                            if ((DateTime.Now - refund.update_dttm.Value).TotalDays > 10)
                            {
                                RWX_PaymentResponses paymentResponses = context.RWX_PaymentResponses.FirstOrDefault(x => x.InvoiceId == refund.invoice_id.Value);
                                if (callPaypalDispurseAPI(paypal_access_token, paymentResponses))
                                {
                                    paymentResponses.Status = "Dispursed";
                                    refund.refund_status = "CANCELLED";
                                    refund.admin_action = "Package not delivered by seller";
                                    var d = refund.shipping_order_id.Split(';');
                                    voidShipStationLabel(Int32.Parse(d[1]), d[0]);
                                }

                            }



                            /*
                            // TEST Site fees here. Remove after test
                            Invoice invoice = AccountingClient.GetInvoiceByID("admin", refund.invoice_id.Value);

                            IDataContext data = UnityResolver.Get<IDataContext>();
                            invoice.LineItems = AccountingClient.GetLineItemsByInvoice("admin", invoice.ID, 0,
                                                                             0, Strings.Fields.DateStamp, false).List;
                            DTO.LineItem lineItem = new DTO.LineItem
                            {
                                TotalAmount = (invoice.ShippingAmount + invoice.BuyersPremiumAmount) * 2,
                                Quantity = 0,
                                PerUnitAmount = 0.0M,
                                Currency = Cache.SiteProperties[Strings.SiteProperties.SiteCurrency],
                                DateStamp = DateTime.UtcNow,
                                Properties = new Dictionary<string, string>(),
                                Description = "Shipping Fees for Invoice#" + invoice.ID,
                                Owner = data.GetSiteOwner(),
                                Payer = invoice.Owner,
                                ListingID = invoice.LineItems[0].ListingID,
                                Type = Strings.LineItemTypes.Fee,
                                Status = Strings.LineItemStatuses.Pending,
                                BuyersPremiumApplies = false,
                                IsArchived = false

                            };
                            data.AddLineItem("admin", lineItem);

                            Invoice inv = AccountingClient.CreateInvoiceFromLineItem("admin", lineItem.ID);
                            */
                            /*
                             * Invoice updatedInvoice = AccountingClient.GetInvoiceByID("admin", item.InvoiceID.Value);
                            updatedInvoice.Total = updatedInvoice.Total + lineItem.TotalAmount;
                            data.UpdateInvoice("admin", updatedInvoice);
                            */

                        }
                    }
                    else
                    {
                        var orderId = "";
                        var shipmentId = "";
                        if (refund.invoice_id != null)
                            (orderId, shipmentId, refund.tracking_number, refund.shipping_label) =
                                createShipStationReturnOrder(refund.invoice_id.Value, true);
                        refund.shipping_order_id = orderId + ";" + shipmentId;
                    }
                }
            });
            context.SaveChangesAsync();
            return refundList;
        }

        #endregion Refunds

        #region Email Reminders

        private void SendRatingReminder()
        {
            aweFramework context = new aweFramework();

            // Get completed shipments that were updated within the last 2 days
            var completedShipments = context.RWX_PaymentResponses
                .Where(p => p.Status == "Dispursed" && p.UpdatedOn != null && (DateTime.Now - p.UpdatedOn).TotalDays <= 1)
                .Select(ship => new { ship.InvoiceId })
                .ToList();

            // Fetch notifications that were already sent
            var invoiceIds = completedShipments.Select(s => s.InvoiceId).ToList();
            //var existingNotifications = context.RWX_Notifications
            //    .Where(n => invoiceIds.Contains(n.DetailID) && (n.Template == "buyer_rating_reminder" || n.Template == "seller_rating_reminder"))
            //    .ToList();

            var notificationsToQueue = new List<Tuple<string, string, string, string, string, int>>();

            // For each completed shipment, process in-memory to avoid calling GetInvoiceByID in the DB context
            foreach (var shipment in completedShipments)
            {
                // Call GetInvoiceByID in-memory, not in the database context
                var invoice = AccountingClient.GetInvoiceByID("admin", shipment.InvoiceId);
                invoice.LineItems = AccountingClient.GetLineItemsByInvoice("admin", invoice.ID, 0,
                    0, Strings.Fields.DateStamp, false).List;
                var buyer = invoice.Payer;
                var seller = invoice.LineItems[0].Listing.Owner;
                var listingId = invoice.LineItems[0].Listing.ID;
                var listingT = ListingClient.GetListingByIDWithFillLevel("admin", listingId, ListingFillLevels.All);
                var lineItemId = invoice.LineItems[0].ID;

                var allFields = new Dictionary<string, string>
                {
                    { "LineItemID", lineItemId.ToString() },
                    { "Title", listingT.Title },
                    { "ListingID", listingId.ToString()},
                };
                var propertyBag = CommonClient.CreatePropertyBag(allFields);

                // Check if the reminder has already been sent to the buyer
                //if (!existingNotifications.Any(n => n.ReceiverUserName == buyer.UserName && n.Template == "CronJob_FeedbackReminder_Buyer" && n.DetailID == listingId))
                //{
                // Prepare notification to the buyer
                notificationsToQueue.Add(Tuple.Create("admin", seller.UserName, buyer.UserName, "CronJob_FeedbackReminder_Buyer", DetailTypes.PropertyBag, propertyBag.ID));
                //}

                // Check if the reminder has already been sent to the seller
                //if (!existingNotifications.Any(n => n.ReceiverUserName == seller.UserName && n.Template == "CronJob_FeedbackReminder_Seller" && n.DetailID == listingId))
                //{
                // Prepare notification to the seller
                notificationsToQueue.Add(Tuple.Create("admin", buyer.UserName, seller.UserName, "CronJob_FeedbackReminder_Seller", DetailTypes.PropertyBag, propertyBag.ID));
                //}
            }

            // Send all notifications in batch
            foreach (var n in notificationsToQueue)
            {
                NotifierClient.QueueNotification(n.Item1, n.Item2, n.Item3, n.Item4, n.Item5, n.Item6, null, null, null, null, null);
            }
        }

        private void emailReminderForPaidNotShippedInvoices()
        {
            aweFramework framework = new aweFramework();
            List<RWX_Invoices> invoices = framework.RWX_Invoices.Where(p => ((p.Status == "Paid") && (!p.Shipped))).ToList();
            invoices.ForEach(inv =>
            {
                try
                {
                    Invoice invoice = AccountingClient.GetInvoiceByID("admin", inv.Id);
                    var seller_email = invoice.Owner.Email;
                    NotifierClient.QueueNotification("admin", "admin",
                                                    null,
                                                    "shipping_reminder", DetailTypes.Listing, inv.Id, null, null, seller_email, null, null);
                }
                catch (Exception ex)
                {
                    LogManager.WriteLog("Email Reminder Failed", "Global.emailReminderForPaidNotShippedInvoices", "global.asax.cs"
                            , TraceEventType.Error, null, ex, new Dictionary<string, object>() { });
                }
            });
        }

        private static void SendInvoiceReminderNotification(RWX_Invoices inv, string sellerEmailTemplate, string buyerEmailTemplate)
        {
            try
            {
                var cultureInfo = CultureInfo.GetCultureInfo(FieldDefaults.Culture);
                var currLineItem = AccountingClient.GetLineItemsByInvoice("admin", inv.Id, 0, 0, Strings.Fields.DateStamp, false).List;

                var allFields = new Dictionary<string, string>
            {
                { "Title", currLineItem[0].Description},
                { "Receiver_Name", currLineItem[0].Listing.OwnerUserName },
                { "Listing_ID", currLineItem[0].ListingID.ToString() },
                { "Invoice_ID", currLineItem[0].InvoiceID.ToString() },
                { "CurrentBidAmount", inv.Subtotal.ToString("C", cultureInfo) },
                { "CurrentWinner", currLineItem[0].Listing.CurrentListingActionUserName },
            };

                var propertyBag = CommonClient.CreatePropertyBag(allFields);

                if (sellerEmailTemplate != null)
                {
                    NotifierClient.QueueNotification(
                        "tgoyal-admin",
                        "Magnolia Pearl Trade Team",
                        currLineItem[0].Listing.OwnerUserName,
                        sellerEmailTemplate,
                        DetailTypes.PropertyBag,
                        propertyBag.ID,
                        null,
                        null,
                        null,
                        null,
                        null);
                }

                if (buyerEmailTemplate != null)
                {
                    NotifierClient.QueueNotification(
                        "tgoyal-admin",
                        "Magnolia Pearl Trade Team",
                        currLineItem[0].Listing.CurrentListingActionUserName,
                        buyerEmailTemplate,
                        DetailTypes.PropertyBag,
                        propertyBag.ID,
                        null,
                        null,
                        null,
                        null,
                        null);
                }
            }
            catch (Exception ex)
            {
                LogManager.WriteLog(ex.Message, "Email Error: EmailUnpaidInvoiceReminder() in Cron Job Global.asax.cs", "MVC", TraceEventType.Error, null, ex);
            }
        }


        #endregion Email Reminders

        #region PayPal Payments

        private List<RWX_PaymentResponses> dispursePaypalPayment(List<RWX_PaymentResponses> completedShipments)
        {
            List<RWX_PaymentResponses> result = new List<RWX_PaymentResponses>();
            // Get Access Token
            string accessToken = getPaypalAccessToken();

            if (accessToken != null)
            {
                //Dispurse amount
                completedShipments.ForEach(shippedorderes =>
                {
                    // DONE add three day gap before amount dispursal
                    if (shippedorderes.Status.Equals("Delivered"))
                    {
                        if (shippedorderes.UpdatedOn != null && (DateTime.Now - shippedorderes.UpdatedOn).TotalDays > 1)
                        {
                            RainWorx.FrameWorx.DTO.Invoice inv = AccountingClient.GetInvoiceByID("admin", shippedorderes.InvoiceId);
                            //if (inv != null && inv.Owner.Email.Equals("<EMAIL>"))
                            //{
                            //    shippedorderes.Status = "Dispursed";
                            //    result.Add(shippedorderes);
                            //}
                            //else 
                            if (inv != null && callPaypalDispurseAPI(accessToken, shippedorderes))
                            {
                                shippedorderes.Status = "Dispursed";
                                result.Add(shippedorderes);
                            }
                        }
                    }
                });
            }
            else
            {
                return null;
            }
            return result;
        }

        private string getPaypalAccessToken(int counter = 0)
        {
            try
            {
                using (var client = new HttpClient())
                {
                    var uri = new Uri(ConfigurationManager.AppSettings["PayPalTokenAPIURL"].ToString());
                    client.DefaultRequestHeaders.Authorization =
                        new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", ConfigurationManager.AppSettings["PayPalTokenAuthString"].ToString());
                    var nvc = new List<KeyValuePair<string, string>>
                {
                    new KeyValuePair<string, string>("grant_type", "client_credentials")
                };
                    var req = new HttpRequestMessage(HttpMethod.Post, uri) { Content = new FormUrlEncodedContent(nvc) };
                    var response = client.SendAsync(req).Result;
                    Debug.WriteLine($"Response: {response}");

                    if (response.IsSuccessStatusCode)
                    {
                        var responseString = response.Content.ReadAsStringAsync().Result.ToString();
                        Debug.WriteLine(responseString);
                        JObject responseObject = JObject.Parse(responseString);
                        Debug.WriteLine(responseObject);
                        return responseObject["access_token"].ToString();
                    }
                    else
                    {
                        var responseString = response.Content.ReadAsStringAsync().Result.ToString();
                        LogManager.WriteLog(responseString, "Global.getPaypalAccessToken", "global.asax.cs"
                            , TraceEventType.Error, null, null, new Dictionary<string, object>() { });
                        if (counter < 3)
                        {
                            return getPaypalAccessToken(counter + 1);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                LogManager.WriteLog("API call failed", "Global.getPaypalAccessToken", "global.asax.cs"
                            , TraceEventType.Error, null, e, new Dictionary<string, object>() { });
                if (counter < 3)
                {
                    return getPaypalAccessToken(counter + 1);
                }
            }
            return null;
        }

        private bool callPaypalDispurseAPI(string accessToken, RWX_PaymentResponses shippedorderes, int counter = 0)
        {
            try
            {
                using (var client = new HttpClient())
                {
                    var request = new
                    {
                        invoice_id = shippedorderes.InvoiceId.ToString(),
                        reference_type = "TRANSACTION_ID",
                        reference_id = shippedorderes.TransactionID.ToString()
                    };
                    var uri = new Uri(ConfigurationManager.AppSettings["PayPalDispursementURL"].ToString());
                    client.DefaultRequestHeaders.Authorization =
                        new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                    client.DefaultRequestHeaders.Add("PayPal-Partner-Attribution-Id", "MagnoliaPearlTrade_SP_PPCP");
                    var json = new JavaScriptSerializer().Serialize(request);
                    var stringContent = new StringContent(json, Encoding.UTF8, "application/json");
                    var response = client.PostAsync(uri, stringContent).Result;
                    if (response.IsSuccessStatusCode)
                    {
                        var responseString = response.Content.ReadAsStringAsync().Result.ToString();
                        Debug.WriteLine(responseString);
                        JObject responseObject = JObject.Parse(responseString);
                        Debug.WriteLine(responseObject);
                        LogManager.WriteLog($"Invoice: {shippedorderes.InvoiceId}, Dispurse: Successful", "Global.callPaypalDispurseAPI", "global.asax.cs"
                            , TraceEventType.Information, null, null, new Dictionary<string, object>() { });
                        return true;
                    }
                    else
                    {
                        var responseString = response.Content.ReadAsStringAsync().Result.ToString();
                        var responseObject = JObject.Parse(responseString);
                        if (responseObject.ContainsKey("name") && responseObject["name"].Equals("TRANSACTION_ALREADY_REVERSED"))
                        {
                            return true;
                        }
                        else if (responseObject.ContainsKey("name") && responseObject["name"].Equals("PAYOUT_ALREADY_COMPLETED_FOR_REFERENCE"))
                        {
                            return true;
                        }
                        LogManager.WriteLog($"Invoice: {shippedorderes.InvoiceId}, Dispurse: Error, Response: {responseString}", "Global.callPaypalDispurseAPI", "global.asax.cs"
                            , TraceEventType.Error, null, null, new Dictionary<string, object>() { });
                        if (counter < 3)
                        {
                            return callPaypalDispurseAPI(accessToken, shippedorderes, counter + 1);
                        }
                    }
                    return false;
                }
            }
            catch (Exception e)
            {
                LogManager.WriteLog("API call failed", "Global.callPaypalDispurseAPI", "global.asax.cs"
                            , TraceEventType.Error, null, e, new Dictionary<string, object>() { });
                if (counter < 3)
                {
                    return callPaypalDispurseAPI(accessToken, shippedorderes, counter + 1);
                }
            }
            return false;
        }

        private string getJWTToken(string businessEmail)
        {
            var clientId = ConfigurationManager.AppSettings["PaypalClientId"].ToString();
            var sellerPayerId = businessEmail;
            var header = new JwtHeader
            {
                { "alg" , "none" }
            };
            var payload = new JwtPayload
            {
                    { "iss", clientId },
                    {"payer_id",  sellerPayerId }
            };
            var secToken = new JwtSecurityToken(header, payload);
            var handler = new JwtSecurityTokenHandler();
            return handler.WriteToken(secToken); ;
        }

        #region PayPal Refund
        public String paypalIssueCompleteRefund(String access_token, String ownerEmail, String transactionId)
        {
            var jwt = getJWTToken(ownerEmail);
            try
            {
                using (var client = new HttpClient())
                {
                    var uri = new Uri(ConfigurationManager.AppSettings["PayPalRefundURL"].ToString() + transactionId + "/refund");
                    client.DefaultRequestHeaders.Authorization =
                        new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", access_token);
                    client.DefaultRequestHeaders.Add("PayPal-Partner-Attribution-Id", "MagnoliaPearlTrade_SP_PPCP");
                    client.DefaultRequestHeaders.Add("PayPal-Auth-Assertion", jwt);
                    var json = new JavaScriptSerializer().Serialize(new { });
                    var stringContent = new StringContent(json, Encoding.UTF8, "application/json");
                    var response = client.PostAsync(uri, stringContent).Result;
                    if (response.IsSuccessStatusCode)
                    {
                        var responseString = response.Content.ReadAsStringAsync().Result.ToString();
                        JObject responseObj = JObject.Parse(responseString);
                        Debug.WriteLine(responseString);
                        return responseObj["id"].ToString();

                    }
                    else
                    {
                        var responseString = response.Content.ReadAsStringAsync().Result.ToString();
                        LogManager.WriteLog(responseString, "AccountController.captureResult", "global.asax.cs"
                            , TraceEventType.Error, null, null, new Dictionary<string, object>() { });
                        //PrepareErrorMessage("SetInvoicePaid", MessageType.Method);
                    }
                }
            }
            catch (Exception e)
            {
                LogManager.WriteLog("API call failed", "AccountController.paypalIssueCompleteRefund", "global.asax.cs"
                            , TraceEventType.Error, null, e, new Dictionary<string, object>() { });
                //PrepareErrorMessage("SetInvoicePaid", MessageType.Method);
            }
            return null;
        }

        #endregion PayPal Refund

        #endregion PayPal Payments

        #region UPS Delivery Helpers
        private List<RWX_PaymentResponses> getCompletedShipments(List<RWX_PaymentResponses> shippedorderes)
        {
            List<RWX_PaymentResponses> result = new List<RWX_PaymentResponses>();
            string upsAccessToken = getUPSAccessToken();
            if (upsAccessToken != null)
            {
                shippedorderes.ForEach(ship =>
                {
                    if (ship.Status != "Delivered")
                    {
                        var extras = ship.AllFields;
                        var splittedData = (extras != null) ? extras.Split(';') : new string[] { };

                        List<string> trackingNumbers = new List<string>();
                        var avalaraId = "";
                        var shipstationOrderId = "";
                        for (var i = 0; i < splittedData.Length; i++)
                        {
                            var isplit = splittedData[i].Split(':');
                            if (isplit[0].Equals("trackingNumber"))
                            {
                                trackingNumbers.Add(isplit[1]);
                            }
                            else if (isplit[0].Equals("AvalaraId"))
                            {
                                avalaraId = isplit[1];
                            }
                            else if (isplit[0].Equals("orderIdShipStation"))
                            {
                                shipstationOrderId = isplit[1];
                            }
                        }
                        var details = "";
                        if (shipstationOrderId.Equals(""))
                        {
                            details += createShipStationOrder(ship.InvoiceId, true);
                        }
                        else if (trackingNumbers.Count > 0 && checkIfDelivered(upsAccessToken, trackingNumbers, ship.InvoiceId))
                        {
                            ship.Status = "Delivered";
                            ship.UpdatedOn = DateTime.Now;
                            result.Add(ship);

                            AccountingClient.SetInvoiceShipped("admin", ship.InvoiceId, true);

                        }

                        if (avalaraId.Equals(""))
                        {
                            RainWorx.FrameWorx.DTO.Invoice inv = AccountingClient.GetInvoiceByID("admin", ship.InvoiceId);
                            inv.LineItems = AccountingClient.GetLineItemsByInvoice("admin", inv.ID, 0,
                                                                                     0, Strings.Fields.DateStamp, false).List;
                            details += sendSalesTaxToAvlara(inv);
                        }

                        if (details.Length > 0)
                        {
                            ship.AllFields += details;
                        }
                    }
                    else
                    {
                        result.Add(ship);
                    }
                });
            }
            return result;
        }

        private bool checkIfDelivered(string accessToken, List<string> trackingList, int invoiceId = -1)
        {
            bool isDelivered = false;
            for (int i = 0; i < trackingList.Count; i++)
            {
                string trackingNumber = trackingList[i];
                if (trackingNumber.Equals(""))
                {
                    continue;
                }
                try
                {
                    using (var client = new HttpClient())
                    {
                        Random random = new Random();
                        var url = new Uri(ConfigurationManager.AppSettings["UPSBaseURL"].ToString() + "api/track/v1/details/" + trackingNumber);
                        //client.DefaultRequestHeaders.Authorization =
                        //    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                        //client.DefaultRequestHeaders.Add("transId", random.Next(999999999).ToString());
                        //client.DefaultRequestHeaders.Add("transactionSrc", "MPTPlatform");

                        HttpRequestMessage req = new HttpRequestMessage
                        {
                            RequestUri = url,
                            Method = HttpMethod.Get,
                            Headers =
                    {
                        { HttpRequestHeader.Authorization.ToString(), "Bearer " +  accessToken },
                        { "transId",  random.Next(999999999).ToString() },
                        { "transactionSrc", "MPTPlatform" },
                        { HttpRequestHeader.Accept.ToString(), "application/json" }
                    }
                        };
                        var response = client.SendAsync(req).Result;
                        Debug.WriteLine(response);
                        if (response.IsSuccessStatusCode)
                        {
                            Debug.WriteLine(response);
                            Debug.WriteLine(response.Content);
                            var responseString = response.Content.ReadAsStringAsync().Result.ToString();
                            Debug.WriteLine(responseString);
                            var responseObject = JObject.Parse(responseString);
                            var deliveryDetails = responseObject["trackResponse"]["shipment"][0]["package"][0]["deliveryDate"];
                            if (deliveryDetails.Any())
                            {
                                var deliveryState = deliveryDetails[0]["type"].ToString();
                                Debug.WriteLine($"Stateeeeeeeeeee: {deliveryState}");

                                if (deliveryState.Equals("DEL"))
                                {
                                    // Delivered package
                                    var deliveryDate = deliveryDetails[0]["date"].ToString();
                                    var delDate = DateTime.ParseExact(deliveryDate, "yyyyMMdd", CultureInfo.InvariantCulture);
                                    /*
                                    if ((DateTime.Now.Date - delDate.Date).Days > 3)
                                    {
                                        
                                    }
                                    */
                                    isDelivered = true;
                                    break;
                                }
                                else if (invoiceId != -1)
                                {
                                    try
                                    {
                                        var activityList = new JArray(responseObject["trackResponse"]["shipment"][0]["package"][0]["activity"]);
                                        for (var index = 0; index < activityList.Count(); index++)
                                        {
                                            var activity = activityList[index];
                                            if (activity.Contains("status") && activity["status"]["type"].Equals("I"))
                                            {
                                                AccountingClient.SetInvoiceShipped("admin", invoiceId, true);
                                                break;
                                            }
                                        }
                                    }
                                    catch (Exception e)
                                    {
                                        Debug.WriteLine(e);
                                        LogManager.WriteLog("Package in transit check failed.", "Global.checkIfDelivered", "global.asax.cs"
                                            , TraceEventType.Error, null, e, new Dictionary<string, object>() { });
                                    }
                                }
                            }
                            else
                            {
                                Debug.WriteLine("Package Details not available");
                            }
                        }
                        else
                        {
                            Debug.WriteLine("API failure!!!!!!!!!!!!!!!!!!!!!!!");

                        }

                    }
                }
                catch (Exception e)
                {
                    LogManager.WriteLog("API call failed", "Global.checkIfDelivered", "global.asax.cs"
                                , TraceEventType.Error, null, e, new Dictionary<string, object>() { });
                }
            }

            return isDelivered;
            //throw new NotImplementedException();
        }

        private string getUPSAccessToken(int counter = 0)
        {
            try
            {
                using (var client = new HttpClient())
                {
                    var uri = new Uri(ConfigurationManager.AppSettings["UPSBaseURL"].ToString() + "security/v1/oauth/token");
                    client.DefaultRequestHeaders.Authorization =
                        new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", ConfigurationManager.AppSettings["UPSTokenAuthString"].ToString());
                    var nvc = new List<KeyValuePair<string, string>>
                    {
                        new KeyValuePair<string, string>("grant_type", "client_credentials")
                    };
                    var req = new HttpRequestMessage(HttpMethod.Post, uri) { Content = new FormUrlEncodedContent(nvc) };
                    var response = client.SendAsync(req).Result;
                    Debug.WriteLine($"Response: {response}");

                    if (response.IsSuccessStatusCode)
                    {
                        var responseString = response.Content.ReadAsStringAsync().Result.ToString();
                        Debug.WriteLine(responseString);
                        JObject responseObject = JObject.Parse(responseString);
                        Debug.WriteLine(responseObject);
                        return responseObject["access_token"].ToString();
                    }
                    else
                    {
                        var responseString = response.Content.ReadAsStringAsync().Result.ToString();
                        LogManager.WriteLog(responseString, "Global.getUPSAccessToken", "global.asax.cs"
                            , TraceEventType.Error, null, null, new Dictionary<string, object>() { });
                        if (counter < 3)
                        {
                            return getUPSAccessToken(counter + 1);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                LogManager.WriteLog("API call failed", "Global.getUPSAccessToken", "global.asax.cs"
                            , TraceEventType.Error, null, e, new Dictionary<string, object>() { });
                if (counter < 3)
                {
                    return getUPSAccessToken(counter + 1);
                }
            }
            return null;
        }

        #endregion UPS Delivery Helpers

        #region Avalara Helpers
        private string sendSalesTaxToAvlara(RainWorx.FrameWorx.DTO.Invoice invoice, int counter = 0)
        {
            string returnString = "";
            try
            {
                AvalaraSalesOrderRequest request = new AvalaraSalesOrderRequest();
                request.lines = new List<ItemDetails>();
                foreach (var item in invoice.LineItems)
                {
                    request.lines.Add(new ItemDetails
                    {
                        quantity = item.Quantity,
                        description = item.Description,
                        amount = item.Quantity * item.PerUnitAmount,
                        taxCode = "PC040100",
                        itemCode = item.ListingID.ToString(),
                    });
                }
                request.lines.Add(new ItemDetails
                {
                    quantity = 1,
                    description = "Shipping",
                    amount = invoice.ShippingAmount,
                    taxCode = "FR020100",
                    itemCode = "111",
                });
                request.lines.Add(new ItemDetails
                {
                    quantity = 1,
                    description = "Insurance",
                    amount = invoice.BuyersPremiumAmount,
                    taxCode = "FR070100",
                    itemCode = "112",
                });
                request.type = "SalesInvoice";
                request.companyCode = ConfigurationManager.AppSettings["AvalaraCompanyCode"].ToString();
                request.date = DateTime.Today.ToString("yyyy-MM-dd");
                request.customerCode = invoice.Payer.ID.ToString();
                request.purchaseOrderNo = invoice.ID.ToString();
                request.addresses = new ShippingDetails
                {
                    shipFrom = new RequestAddress
                    {
                        line1 = invoice.OwnerStreet1,
                        line2 = invoice.OwnerStreet2,
                        city = invoice.OwnerCity,
                        region = invoice.OwnerStateRegion,
                        country = invoice.OwnerCountry,
                        postalCode = invoice.OwnerZipPostal
                    },
                    shipTo = new RequestAddress
                    {
                        line1 = invoice.ShippingStreet1 != null ? invoice.ShippingStreet1 : invoice.BillingStreet1,
                        line2 = invoice.ShippingStreet2 != null ? invoice.ShippingStreet2 : invoice.BillingStreet2,
                        city = invoice.ShippingCity != null ? invoice.ShippingCity : invoice.BillingCity,
                        region = invoice.ShippingStateRegion != null ? invoice.ShippingStateRegion : invoice.BillingStateRegion,
                        country = invoice.ShippingCountry != null ? invoice.ShippingCountry : invoice.BillingCountry,
                        postalCode = invoice.ShippingZipPostal != null ? invoice.ShippingZipPostal : invoice.BillingZipPostal
                    }
                };
                request.commit = true;
                request.currencyCode = "USD";
                request.description = "Clothing";


                using (var client = new HttpClient())
                {
                    var uri = new Uri(ConfigurationManager.AppSettings["AvalaraCreateOrderURL"].ToString());
                    client.DefaultRequestHeaders.Authorization =
                        new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", ConfigurationManager.AppSettings["AvalaraCreateAuthString"].ToString());
                    var json = new JavaScriptSerializer().Serialize(request);
                    var stringContent = new StringContent(json, Encoding.UTF8, "application/json");
                    var response = client.PostAsync(uri, stringContent).Result;
                    if (response.IsSuccessStatusCode)
                    {
                        Debug.WriteLine(response);
                        Debug.WriteLine(response.Content);
                        var responseString = response.Content.ReadAsStringAsync().Result.ToString();
                        Debug.WriteLine(responseString);
                        JObject responseObject = JObject.Parse(responseString);
                        var totalTax = responseObject["totalTax"];
                        Debug.WriteLine("Total Tax: ");
                        Debug.WriteLine(totalTax);
                        returnString = "AvalaraId:" + responseObject["id"];
                    }
                    else
                    {
                        var responseString = response.Content.ReadAsStringAsync().Result.ToString();
                        LogManager.WriteLog(responseString, "Global.sendSalesTaxToAvlara", "global.asax.cs"
                            , TraceEventType.Error, null, null, new Dictionary<string, object>() { });
                        if (counter < 3)
                        {
                            return sendSalesTaxToAvlara(invoice, counter + 1);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                LogManager.WriteLog("API call failed", "Global.sendSalesTaxToAvlara", "global.asax.cs"
                            , TraceEventType.Error, null, e, new Dictionary<string, object>() { });
                if (counter < 3)
                {
                    return sendSalesTaxToAvlara(invoice, counter + 1);
                }
            }
            return returnString;
        }

        public string completeRefundAvalara(string avalaraTransactionCode)
        {
            var returnString = "";
            using (var client = new HttpClient())
            {
                string avaTransactionCode = avalaraTransactionCode.Replace("/", "_-ava2f-_")
                                                                  .Replace("+", "_-ava2b-_")
                                                                  .Replace("?", "_-ava3f-_")
                                                                  .Replace("%", "_-ava25-_")
                                                                  .Replace("#", "_-ava23-_")
                                                                  .Replace(" ", "%20");
                var request = new
                {
                    refundDate = DateTime.Now.ToString("yyyy-MM-dd"),
                    refundType = "Full",
                    referenceCode = "Refund for a committed transaction"
                };
                var uri = new Uri(ConfigurationManager.AppSettings["AvalaraRefundURL"].ToString()
                                    + ConfigurationManager.AppSettings["AvalaraCompanyCode"].ToString()
                                    + "/transactions/" + avalaraTransactionCode + "/refund");
                client.DefaultRequestHeaders.Authorization =
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", ConfigurationManager.AppSettings["AvalaraCreateAuthString"].ToString());
                var json = new JavaScriptSerializer().Serialize(request);
                var stringContent = new StringContent(json, Encoding.UTF8, "application/json");
                var response = client.PostAsync(uri, stringContent).Result;
                if (response.IsSuccessStatusCode)
                {
                    Debug.WriteLine(response);
                    Debug.WriteLine(response.Content);
                    var responseString = response.Content.ReadAsStringAsync().Result.ToString();
                    Debug.WriteLine(responseString);
                    JObject responseObject = JObject.Parse(responseString);
                    var totalTax = responseObject["totalTax"];
                    Debug.WriteLine("Total Tax: ");
                    Debug.WriteLine(totalTax);
                    returnString = "AvaRefundId:" + responseObject["id"] + ";AvaRefundCode:" + responseObject["code"];
                }
                else
                {
                    var responseString = response.Content.ReadAsStringAsync().Result.ToString();
                    LogManager.WriteLog(responseString, "PaymentResult.sendSalesTaxToAvlara", "global.asax.cs"
                        , TraceEventType.Error, null, null, new Dictionary<string, object>() { });
                    /*
                    if (counter < 3)
                    {
                        return sendSalesTaxToAvlara(invoice, user_id, counter + 1);
                    }
                    */
                }
                return returnString;
            }
        }

        #endregion Avalara Helpers

        public int markInvoiceUnpaid(Invoice invoice, String refundId, String AvaTaxCode)
        {
            try
            {
                aweFramework context = new aweFramework();

                RWX_PaymentResponses paymentResponses = new RWX_PaymentResponses();
                paymentResponses.CreatedOn = DateTime.Now;
                paymentResponses.UpdatedOn = DateTime.Now;
                paymentResponses.ResponseStatus = "Complete";
                paymentResponses.Description = "Unpaid";
                paymentResponses.AVSResponseCode = "Address";
                paymentResponses.CVV2ResponseCode = "Match";
                paymentResponses.CAVResponseCode = "DataError";
                paymentResponses.DateStamp = DateTime.Now;
                paymentResponses.Provider = "ManualPayment";
                paymentResponses.Approved = true;
                paymentResponses.Amount = -1 * invoice.Total;
                paymentResponses.Method = DTO.PaymentMethod.Other.ToString();
                paymentResponses.BuyerStreet = invoice.ShippingStreet1 != null ? invoice.ShippingStreet1 : invoice.BillingStreet1;
                paymentResponses.BuyerCity = invoice.ShippingCity != null ? invoice.ShippingCity : invoice.BillingCity;
                paymentResponses.BuyerStateRegion = invoice.ShippingStateRegion != null ? invoice.ShippingStateRegion : invoice.BillingStateRegion;
                paymentResponses.BuyerZipPostal = invoice.ShippingZipPostal != null ? invoice.ShippingZipPostal : invoice.BillingZipPostal;
                paymentResponses.InvoiceId = invoice.ID;
                paymentResponses.LastUpdatedUser = "admin";
                paymentResponses.Status = "Refunded";


                // TODO: Enter Refund Details here
                paymentResponses.AllFields = "RefundId:" + refundId + ";" + AvaTaxCode;

                // You can now use the refund object as per your requirements


                context.RWX_PaymentResponses.Add(paymentResponses);

                context.SaveChanges();

                return paymentResponses.Id;
                /*
                Refunds refund = new Refunds();
                refund.InvoiceId = invoice.ID;
                refund.SellerApprovalStatus = "";
                refund.SellersComment = "";
                refund.ReturnReason = "Defective product";
                refund.PaymentResponseId = paymentResponses.Id;
                refund.AdminAction = "Refund";
                refund.AutoApproved = false;
                refund.ReturnType = "Admin Refund";
                refund.SellerId = invoice.Owner.ID;
                refund.BuyerId = invoice.Payer.ID;
                refund.RefundStatus = "COMPLETED";

                context.Refunds.Add(refund);

                context.SaveChanges();
                */
                /*
                var commandText = "UPDATE [magnoliapearltrade].[dbo].[RWX_LineItems] SET [Status] ='Complete' WHERE [InvoiceId] = @inv_id";
                var inv_id = new SqlParameter("@inv_id", inv.ID); ;
                context.Database.ExecuteSqlCommand(commandText, new[] { inv_id });
                */
                // Void Shipment



                //PrepareSuccessMessage("SetInvoicePaid", MessageType.Method);
            }
            catch (FaultException<InvalidOperationFaultContract> iofc)
            {
                //PrepareErrorMessage(iofc.Detail.Reason);
            }
            catch (FaultException<AuthorizationFaultContract> afc)
            {
                //PrepareErrorMessage(afc.Detail.Reason);
            }
            catch
            {
                //PrepareErrorMessage("SetInvoicePaid", MessageType.Method);
            }
            return -10;
        }

        #region ShipStation Helpers
        string createShipStationOrder(int invoiceId, bool paid, int counter = 0)
        {
            string returnData = "";
            try
            {
                ShipStationClient ssclient = new ShipStationClient();
                var response = ssclient.CreateOrder(invoiceId, paid);
                if (response.IsSuccessStatusCode)
                {
                    var rstr = response.Content.ReadAsStringAsync().Result.ToString();
                    JObject responseObject = JObject.Parse(rstr);
                    var order_id = responseObject["orderId"].ToString();

                    returnData = "orderIdShipStation:" + order_id;

                    int ctr = 0;
                    while (ctr < 3)
                    {
                        RainWorx.FrameWorx.DTO.Invoice inv = AccountingClient.GetInvoiceByID("admin", invoiceId);
                        var resp = ssclient.generateShippingLabel(inv, order_id);
                        if (resp.IsSuccessStatusCode)
                        {
                            var responseString = resp.Content.ReadAsStringAsync().Result.ToString();
                            Debug.WriteLine(responseString);
                            JObject responseObjectLabel = JObject.Parse(responseString);
                            var shipmentId = responseObjectLabel["shipmentId"];
                            var trackingNumber = responseObjectLabel["trackingNumber"];
                            var label = responseObjectLabel["labelData"];
                            returnData += ";shipmentId:" + shipmentId + ";trackingNumber:" + trackingNumber + ";label:" + label;
                            ssclient.SendEmailWithLabel(invoiceId, label.ToString(), trackingNumber.ToString(), "admin");
                            ssclient.AttachLabelToOrder(order_id, trackingNumber.ToString());
                            //var ins_id = ssclient.createInsurance(inv, trackingNumber.ToString(), false);
                            //returnData += ";insuranceId:" + ins_id;
                            break;
                        }
                        else
                        {
                            var responseString = resp.Content.ReadAsStringAsync().Result.ToString();
                            LogManager.WriteLog(responseString, $"PaymentResult.createShipStationOrder Shipping Label Error {invoiceId}", "global.asax.cs"
                                     , TraceEventType.Error, null, null, new Dictionary<string, object>() { });
                            Debug.WriteLine(responseString);
                            ctr++;
                        }
                    }
                }
                else
                {
                    var responseString = response.Content.ReadAsStringAsync().Result.ToString();
                    LogManager.WriteLog(responseString, $"Global.createShipStationOrder Invoice {invoiceId}", "global.asax.cs"
                                 , TraceEventType.Error, null, null, new Dictionary<string, object>() { });
                    return createShipStationOrder(invoiceId, paid, counter + 1);
                }
            }
            catch (Exception e)
            {
                LogManager.WriteLog("API call failed", $"Global.createShipStationOrder.GenerateLabel Invoice {invoiceId}", "global.asax.cs"
                            , TraceEventType.Error, null, e, new Dictionary<string, object>() { });
                if (counter < 3)
                {
                    return createShipStationOrder(invoiceId, paid, counter + 1);
                }
            }
            return returnData;
        }

        (string order_id, string shipmentId, string tracking_number, string label) createShipStationReturnOrder(int invoiceId, bool paid, int counter = 0)
        {
            string order_id = "";
            string tracking_number = "";
            string label = "";
            string shipmentId = "";
            try
            {
                ShipStationClient ssclient = new ShipStationClient();
                var response = ssclient.CreateOrder(invoiceId, paid, true);
                if (response.IsSuccessStatusCode)
                {
                    var rstr = response.Content.ReadAsStringAsync().Result.ToString();
                    JObject responseObject = JObject.Parse(rstr);
                    order_id = responseObject["orderId"].ToString();

                    //returnData = "orderIdShipStation:" + order_id;

                    int ctr = 0;
                    while (ctr < 3)
                    {
                        RainWorx.FrameWorx.DTO.Invoice inv = AccountingClient.GetInvoiceByID("admin", invoiceId);
                        var resp = ssclient.generateShippingLabel(inv, order_id, true);
                        if (resp.IsSuccessStatusCode)
                        {
                            var responseString = resp.Content.ReadAsStringAsync().Result.ToString();
                            Debug.WriteLine(responseString);
                            JObject responseObjectLabel = JObject.Parse(responseString);
                            shipmentId = responseObjectLabel["shipmentId"].ToString();
                            tracking_number = responseObjectLabel["trackingNumber"].ToString();
                            label = responseObjectLabel["labelData"].ToString();
                            //returnData += ";trackingNumber:" + trackingNumber + ";label:" + label + ";";
                            ssclient.SendEmailWithLabel(invoiceId, label, tracking_number, "admin", true);
                            ssclient.AttachLabelToOrder(order_id, tracking_number);
                            //ssclient.createInsurance(inv, tracking_number.ToString(), false);
                            break;
                        }
                        else
                        {
                            var responseString = resp.Content.ReadAsStringAsync().Result.ToString();
                            LogManager.WriteLog(responseString, $"Global.createShipStationOrder Shipping Label Error {invoiceId}", "global.asax.cs"
                                     , TraceEventType.Error, null, null, new Dictionary<string, object>() { });
                            Debug.WriteLine(responseString);
                            ctr++;
                        }
                    }
                }
                else
                {
                    var responseString = response.Content.ReadAsStringAsync().Result.ToString();
                    LogManager.WriteLog(responseString, $"Global.createShipStationOrder {invoiceId}", "global.asax.cs"
                                 , TraceEventType.Error, null, null, new Dictionary<string, object>() { });
                    if (counter < 3)
                    {
                        return createShipStationReturnOrder(invoiceId, paid, counter + 1);
                    }
                }
            }
            catch (Exception e)
            {
                LogManager.WriteLog("API call failed", $"Global.createShipStationOrder {invoiceId}", "global.asax.cs"
                            , TraceEventType.Error, null, e, new Dictionary<string, object>() { });
                if (counter < 3)
                {
                    return createShipStationReturnOrder(invoiceId, paid, counter + 1);
                }
            }
            return (order_id, shipmentId, tracking_number, label);
        }

        public bool voidShipStationLabel(int ShipstationShipmentId, string shipstationOrderId)
        {
            try
            {
                var request = new
                {
                    shipmentId = ShipstationShipmentId
                };
                using (var client = new HttpClient())
                {
                    var uri = new Uri(ConfigurationManager.AppSettings["ShipStationLabelVoidURL"].ToString());
                    client.DefaultRequestHeaders.Authorization =
                        new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", ConfigurationManager.AppSettings["ShipStationBasicAuthString"].ToString());
                    var json = new JavaScriptSerializer().Serialize(request);
                    var stringContent = new StringContent(json, Encoding.UTF8, "application/json");
                    var response = client.PostAsync(uri, stringContent).Result;
                    if (response.IsSuccessStatusCode)
                    {
                        var responseString = response.Content.ReadAsStringAsync().Result.ToString();
                        Debug.WriteLine(responseString);
                        return deleteShippingOrder(shipstationOrderId);
                    }
                    else
                    {
                        LogManager.WriteLog("API call failed", "Global.asax.voidShipStationLabel", "global.asax.cs"
                            , TraceEventType.Error, null, null, new Dictionary<string, object>() { });
                        // PrepareErrorMessage("SetInvoicePaid", MessageType.Method);
                    }
                }
            }
            catch (Exception e)
            {
                LogManager.WriteLog("API call failed", "Global.asax.voidShipStationLabel", "global.asax.cs"
                            , TraceEventType.Error, null, e, new Dictionary<string, object>() { });
                // PrepareErrorMessage("SetInvoicePaid", MessageType.Method);
            }
            return false;
        }

        public bool deleteShippingOrder(string shipstationOrderId)
        {
            using (var client = new HttpClient())
            {
                var uri = new Uri(ConfigurationManager.AppSettings["ShipStationAPIAddress"].ToString() + "orders/" + shipstationOrderId);
                client.DefaultRequestHeaders.Authorization =
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", ConfigurationManager.AppSettings["ShipStationBasicAuthString"].ToString());
                var response = client.DeleteAsync(uri).Result;
                if (response.IsSuccessStatusCode)
                {
                    var responseString = response.Content.ReadAsStringAsync().Result.ToString();
                    Debug.WriteLine(responseString);

                    return true;
                }
            }
            return false;
        }

        #endregion ShipStation Helpers

        #region Google Shopping Campaign

        public void uploadListingsToGoogle(bool all_listings = false)
        {
            try
            {
                Console.WriteLine("Uploading data to Google");
                var today = DateTime.Today;
                aweFramework framework = new aweFramework();

                List<RWX_Listings> recently_updated_listings = null;

                if (all_listings)
                {
                    recently_updated_listings = framework.RWX_Listings.Where(field => field.Status == "Active").ToList();
                }
                else
                {
                    recently_updated_listings = framework.RWX_Listings.Where(field => DbFunctions.TruncateTime(field.UpdatedOn) == today || DbFunctions.TruncateTime(field.EndDTTM) == today).ToList();
                }
                string sCurrentDirectory = AppDomain.CurrentDomain.BaseDirectory;
                string sFile = System.IO.Path.Combine(sCurrentDirectory, @"content-api-key.json");
                string sFilePath = Path.GetFullPath(sFile);

                ulong merchantId = ulong.Parse(ConfigurationManager.AppSettings["GoogleShoppingMerchantId"].ToString());
                string[] scopes = new string[] { ShoppingContentService.Scope.Content };
                var credential = GoogleCredential.FromFile(sFilePath).CreateScoped(scopes);
                ProductsUtil productsUtil = new ProductsUtil(
                        new ShoppingContentService(new BaseClientService.Initializer
                        {
                            ApplicationName = "GoogleShoppingFeed",
                            HttpClientInitializer = credential
                        }),
                        10
                    );

                List<GoogleProductMappings> google_mapping = framework.GoogleProductMappings.Where(field => field.status == "Active").ToList();
                Dictionary<int, string> map = new Dictionary<int, string>();
                Dictionary<int, int> index_map = new Dictionary<int, int>();

                int i = 0;
                foreach (GoogleProductMappings mapping in google_mapping)
                {
                    map.Add(mapping.listing_id, mapping.google_product_id);
                    index_map.Add(mapping.listing_id, i);
                    i++;
                }
                // productsUtil.GetAllProducts(merchantId);
                foreach (RWX_Listings listing_table in recently_updated_listings)
                {
                    DTO.Listing listing = ListingClient.GetListingByIDWithFillLevel("admin", listing_table.Id, ListingFillLevels.All);
                    if (listing.Owner.Email.ToLower().Equals("<EMAIL>") || listing.Owner.Email.ToLower().Equals("<EMAIL>") || listing.Owner.Email.ToLower().Equals("<EMAIL>") || listing.Owner.Email.ToLower().Equals("<EMAIL>"))
                    {
                        if (listing_table.Status == "Active")
                        {
                            // DTO.Listing listing = ListingClient.GetListingByIDWithFillLevel("admin", listing_table.Id, ListingFillLevels.All);
                            string product_id = map.GetOrNull(listing.ID);
                            if (product_id == null)
                            {
                                Google.Apis.ShoppingContent.v2_1.Data.Product prod_id_google = productsUtil.InsertProduct(merchantId, listing);
                                GoogleProductMappings googleProductMapping = new GoogleProductMappings();
                                googleProductMapping.listing_id = listing_table.Id;
                                googleProductMapping.google_product_id = prod_id_google.Id;
                                googleProductMapping.status = "Active";
                                framework.GoogleProductMappings.Add(googleProductMapping);

                            }
                            else
                            {
                                productsUtil.UpdateProduct(merchantId, product_id, listing);
                            }
                        }
                        else
                        {
                            string product_id = map.GetOrNull(listing_table.Id);
                            if (product_id != null)
                            {
                                productsUtil.DeleteProduct(merchantId, product_id);
                                google_mapping.ElementAt(index_map.GetOrNull(listing_table.Id)).status = "Inactive";
                            }

                        }
                    }

                }
                framework.SaveChangesAsync();
            }
            catch (Exception e)
            {
                LogManager.WriteLog("Google Shopping API call failed", "Global.asax.uploadListingsToGoogle", "global.asax.cs"
                           , TraceEventType.Error, null, e, new Dictionary<string, object>() { });
            }

        }

        #endregion

        #region Monthly Commission Report

        /// <summary>
        /// Extracts the Refund ID from the given string.
        /// </summary>
        /// <param name="allFields">The string containing the Refund ID.</param>
        /// <returns>The extracted Refund ID, or null if not found.</returns>
        public string GetRefundId(string allFields)
        {
            const string pattern = @"RefundId:([a-zA-Z0-9]+);";
            var match = Regex.Match(allFields, pattern);

            return match.Success ? match.Groups[1].Value : null;
        }

        /// <summary>
        /// Generates and sends the commission report for the specified month.
        /// </summary>
        /// <param name="monthDiff">The number of months to look back. Default is 1.</param>
        public void commissionReport(int monthDiff = 1)
        {
            try
            {
                aweFramework context = new aweFramework();
                var today = DateTime.Today;
                var curr_month = new DateTime(today.Year, today.Month, 1);

                // Monthly report date range
                var first_day = curr_month.AddMonths(-monthDiff);
                var last_day = curr_month.AddHours(6); // 6-hour offset

                // Generate monthly report
                var monthlyReport = GenerateCommissionReport(context, first_day, last_day);

                // Write monthly CSV
                const string folder = "~/Reports";
                var monthlyReportName = $"{getFullName(first_day.Month)}-{first_day.Year}.csv";
                var monthlyFileName = $"{folder}/{monthlyReportName}";
                EnsureDirectoryExists(folder);
                WriteReportToCsv(monthlyReport, monthlyFileName);

                // Prepare for yearly report
                List<CommissionReport> yearlyReport = null;
                string yearlyReportName = "";
                string yearlyFileName = "";

                if (curr_month.Month == 1)
                {
                    // If it's January, we want the previous year's report,
                    // but ending on Jan 1 + 6 hours of the current year
                    // e.g., if current_month is Jan 2025, 
                    // yearly report = Jan 1, 2024 (00:00) to Jan 1, 2025 (06:00)
                    int previousYear = curr_month.Year - 1;
                    var yearlyFirstDay = new DateTime(previousYear, 1, 1);
                    var yearlyLastDay = curr_month.AddHours(6);

                    yearlyReport = GenerateCommissionReport(context, yearlyFirstDay, yearlyLastDay);

                    // Example file name: YearlyReport_2024-Jan-to-Dec.csv
                    yearlyReportName = $"YearlyReport_{previousYear}-Jan-to-Dec.csv";
                    yearlyFileName = $"{folder}/{yearlyReportName}";
                    WriteReportToCsv(yearlyReport, yearlyFileName);
                }
                else if (curr_month.Month - 1 != 1)
                {
                    // Not January: Yearly report from Jan 1 of current year to the 'last_day' 
                    // (which is already month start + 6 hours)
                    var yearlyFirstDay = new DateTime(curr_month.Year, 1, 1);
                    var yearlyLastDay = last_day; // e.g., if it's April, then 1st April + 6h

                    yearlyReport = GenerateCommissionReport(context, yearlyFirstDay, yearlyLastDay);

                    // Example: YearlyReport_2025-Jan-to-Mar.csv 
                    // (depends on getFullName(curr_month.AddMonths(-1).Month))
                    yearlyReportName = $"YearlyReport_{yearlyFirstDay.Year}-Jan-to-{getFullName(curr_month.AddMonths(-1).Month)}.csv";
                    yearlyFileName = $"{folder}/{yearlyReportName}";
                    WriteReportToCsv(yearlyReport, yearlyFileName);
                }

                // Prepare email details
                var emails = new List<string>
                {
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>"
                };
                var ccEmails = new List<string>
                {
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>"
                };
                var bccEmails = new List<string> { };

                var subject = $"Commission Report for {getFullName(first_day.Month)} {first_day.Year}";
                var body =
                    $"Hi Team,\n\n" +
                    $"PFA the commission report for {getFullName(first_day.Month)} {first_day.Year}.\n\n" +
                    $"Regards,\nMagnolia Pearl Team.\n\n" +
                    $"Please note: This is an automated email. Please reach out to the dev team for any queries.";

                const string mimeType = "text/csv";

                // Always include the monthly report
                var attachments = new List<(string Base64, string Name, string MimeType)>
                {
                    (
                        Convert.ToBase64String(File.ReadAllBytes(HostingEnvironment.MapPath(monthlyFileName))),
                        monthlyReportName,
                        mimeType
                    )
                };

                // If yearly file exists, attach that as well
                if (!string.IsNullOrEmpty(yearlyFileName))
                {
                    var filePath = HostingEnvironment.MapPath(yearlyFileName);

                    if (File.Exists(filePath))
                    {
                        attachments.Add
                        (
                            (
                                Convert.ToBase64String(File.ReadAllBytes(filePath)),
                                yearlyReportName,
                                mimeType
                            )
                        );
                    }
                }

                // Send the email
                var ssClient = new ShipStationClient();
                ssClient.SendEmailWithAttachments(emails, ccEmails, bccEmails, subject, body, attachments);

                Debug.WriteLine("Done");
            }
            catch (Exception ex)
            {
                Debug.WriteLine(ex.ToString());
                LogManager.WriteLog
                (
                    "Commission Report Generation Error",
                    "Global.asax.commissionReport",
                    "global.asax.cs",
                    TraceEventType.Error,
                    null,
                    ex,
                    new Dictionary<string, object>()
                );
            }
        }

        private List<CommissionReport> GenerateCommissionReport(aweFramework context, DateTime first_day, DateTime last_day)
        {
            var invoices = context.RWX_Invoices
                .Where(inv =>
                    inv.Status == "Paid" && inv.PaidDTTM >= first_day && inv.PaidDTTM < last_day &&
                    !context.Refunds.Any(r => inv.Id == r.invoice_id)).OrderBy(inv => inv.PaidDTTM).ToList();

            var refundInvoices = context.RWX_Invoices
                .Where(inv =>
                    inv.Status == "Paid" && inv.PaidDTTM >= first_day && inv.PaidDTTM < last_day &&
                    context.Refunds.Any(r => inv.Id == r.invoice_id)
                    && context.RWX_PaymentResponses.Any(resp => resp.CreatedOn >= first_day && resp.CreatedOn < last_day)
                ).OrderBy(inv => inv.PaidDTTM).ToList();

            var refundPaymentResponseWithInvoices = (from inv in context.RWX_Invoices
                                                     join resp in context.RWX_PaymentResponses
                                                         on inv.Id equals resp.InvoiceId
                                                     where inv.Status == "Paid"
                                                           && resp.Status == "Refunded"
                                                           && resp.CreatedOn >= first_day
                                                           && resp.CreatedOn < last_day
                                                           && context.Refunds.Any(r => r.invoice_id == inv.Id)
                                                     orderby inv.PaidDTTM
                                                     select new
                                                     {
                                                         Invoice = inv,
                                                         PaymentResponse = resp
                                                     }).ToList();

            List<CommissionReport> report = new List<CommissionReport>();

            // Process invoices
            foreach (var invoice in invoices)
            {
                var reportItem = BuildCommissionReportItem(invoice, "No");
                report.Add(reportItem);
            }

            // Process refund invoices
            foreach (var invoice in refundInvoices)
            {
                var reportItem = BuildCommissionReportItem(invoice, "Yes");
                report.Add(reportItem);
            }

            // Process refund data
            foreach (var data in refundPaymentResponseWithInvoices)
            {
                var invoice = data.Invoice;
                var paypalRefundId = GetRefundId(data.PaymentResponse.AllFields);

                AccountController ac = new AccountController();
                var refundData = ac.getRefundPaypalFees(paypalRefundId, invoice.Id);
                if (refundData.Equals(null))
                {
                    LogManager.WriteLog($"Invoice ID: {invoice.Id}, Error while fetching paypal refund data", "Global.asax.commissionReport", "global.asax.cs"
                        , TraceEventType.Error, null, null, new Dictionary<string, object>() { });
                }

                var reportItem = BuildRefundCommissionReportItem(data.PaymentResponse, invoice, refundData);
                report.Add(reportItem);
            }

            return report;
        }

        private CommissionReport BuildCommissionReportItem(RWX_Invoices invoice, string isRefunded)
        {
            Invoice detailedInvoice = AccountingClient.GetInvoiceByID("admin", invoice.Id);
            detailedInvoice.LineItems = AccountingClient.GetLineItemsByInvoice("admin", invoice.Id, 0, 0, Strings.Fields.DateStamp, false).List;

            decimal mpCommissionAmount = invoice.MPCommissionAmount;
            decimal mpCommissionPercent = invoice.MPCommissionPercent;
            decimal? sellerPaypalFees = invoice.SellerPaypalFees;
            double sellerFinalPriceFromMpt = (double)(invoice.Total - (invoice.ShippingAmount +
                                                                       invoice.BuyersPremiumAmount +
                                                                       invoice.SalesTax + mpCommissionAmount));
            double sellerFinalPriceFromPaypal = sellerFinalPriceFromMpt - (double)(sellerPaypalFees ?? 0);

            return new CommissionReport()
            {
                TransactionDate = invoice.PaidDTTM != null ? invoice.PaidDTTM.Value.ToString("yyyy-MM-dd HH:mm:ss") : "",
                InvoiceId = invoice.Id,
                ListingId = detailedInvoice.LineItems[0].ListingID,
                ItemName = detailedInvoice.LineItems[0].Listing.Title,
                OwnerName = invoice.OwnerName,
                OwnerEmail = detailedInvoice.Owner.Email,
                SellerAddress = $"{invoice.OwnerStreet1}, {invoice.OwnerStreet2}, {invoice.OwnerCity}, {invoice.OwnerStateRegion}, {invoice.OwnerZipPostal}, {invoice.OwnerCountry}",
                BuyerAddress = $"{invoice.BillingStreet1}, {invoice.BillingStreet2}, {invoice.BillingCity}, {invoice.BillingStateRegion}, {invoice.BillingZipPostal}, {invoice.BillingCountry}",
                ItemCost = ((double)invoice.Subtotal),
                SalesTax = ((double)invoice.SalesTax),
                Shipping = ((double)invoice.ShippingAmount),
                Insurance = ((double)invoice.BuyersPremiumAmount),
                TotalAmount = ((double)invoice.Total),
                ShippingAdjustment = -((double)(invoice.ShippingAmount + invoice.BuyersPremiumAmount)),
                TaxesAdjustment = -((double)invoice.SalesTax),
                MPCommissionAmount = mpCommissionAmount,
                MPCommissionPercent = mpCommissionPercent,
                SellerFinalPriceFromMPT = sellerFinalPriceFromMpt,
                SellerPaypalFees = sellerPaypalFees,
                SellerFinalPriceFromPaypal = sellerFinalPriceFromPaypal,
                IsRefunded = isRefunded
            };
        }

        private CommissionReport BuildRefundCommissionReportItem(RWX_PaymentResponses paymentResponse, RWX_Invoices invoice, dynamic refundData)
        {
            var detailedInvoice = AccountingClient.GetInvoiceByID("admin", invoice.Id);
            detailedInvoice.LineItems = AccountingClient.GetLineItemsByInvoice("admin", invoice.Id, 0, 0, Strings.Fields.DateStamp, false).List;

            var sellerPaypalFees = refundData.Item1;
            var paypalFeeDiff = invoice.SellerPaypalFees - sellerPaypalFees;
            decimal mpCommissionAmount = invoice.MPCommissionAmount;
            decimal mpCommissionPercent = invoice.MPCommissionPercent;
            double sellerFinalPriceFromMpt = (double)(refundData.Item2 + refundData.Item1);
            double sellerFinalPriceFromPaypal = (double)(refundData.Item2);

            return new CommissionReport()
            {
                TransactionDate = paymentResponse.UpdatedOn.ToString("yyyy-MM-dd HH:mm:ss"),
                InvoiceId = invoice.Id,
                ListingId = detailedInvoice.LineItems[0].ListingID,
                ItemName = detailedInvoice.LineItems[0].Listing.Title,
                OwnerName = invoice.OwnerName,
                OwnerEmail = detailedInvoice.Owner.Email,
                SellerAddress = $"{invoice.OwnerStreet1}, {invoice.OwnerStreet2}, {invoice.OwnerCity}, {invoice.OwnerStateRegion}, {invoice.OwnerZipPostal}, {invoice.OwnerCountry}",
                BuyerAddress = $"{invoice.BillingStreet1}, {invoice.BillingStreet2}, {invoice.BillingCity}, {invoice.BillingStateRegion}, {invoice.BillingZipPostal}, {invoice.BillingCountry}",
                ItemCost = -((double)invoice.Subtotal),
                SalesTax = -((double)invoice.SalesTax),
                Shipping = -((double)invoice.ShippingAmount),
                Insurance = -((double)invoice.BuyersPremiumAmount),
                TotalAmount = -((double)invoice.Total),
                ShippingAdjustment = ((double)(invoice.ShippingAmount + invoice.BuyersPremiumAmount)),
                TaxesAdjustment = ((double)invoice.SalesTax),
                MPCommissionAmount = -mpCommissionAmount,
                MPCommissionPercent = -mpCommissionPercent,
                SellerFinalPriceFromMPT = -sellerFinalPriceFromMpt,
                SellerPaypalFees = -sellerPaypalFees,
                SellerFinalPriceFromPaypal = -sellerFinalPriceFromPaypal,
                IsRefunded = "Refund Data"
            };
        }

        private void EnsureDirectoryExists(string folder)
        {
            var mappedPath = HostingEnvironment.MapPath(folder);
            var folderExists = Directory.Exists(mappedPath);
            if (!folderExists)
            {
                Directory.CreateDirectory(mappedPath);
            }
        }

        private void WriteReportToCsv(List<CommissionReport> report, string fileName)
        {
            using (var writer = new StreamWriter(HostingEnvironment.MapPath(fileName)))
            using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
            {
                csv.WriteRecords(report);
            }
        }

        public string getFullName(int month)
        {
            return CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(month);
        }

        public byte[] writeCsvWithHeaderToMemory<T>(IEnumerable<T> records) where T : class
        {
            using (var memoryStream = new MemoryStream())
            using (var streamWriter = new StreamWriter(memoryStream))
            using (var csvWriter = new CsvWriter(streamWriter, CultureInfo.InvariantCulture))
            {
                csvWriter.WriteRecords<T>(records);

                return memoryStream.ToArray();
            }
        }
        #endregion

        #region Custom Invoice CSV Export

        public class CustomInvoiceCsvRecord
        {
            // Invoice Details
            [Name("Invoice ID")]
            public int InvoiceId { get; set; }

            [Name("Invoice Status")]
            public string InvoiceStatus { get; set; }

            [Name("Invoice Created Date")]
            public string InvoiceCreatedDate { get; set; }

            [Name("Invoice Paid Date")]
            public string InvoicePaidDate { get; set; }

            [Name("Invoice Type")]
            public string InvoiceType { get; set; }

            [Name("Currency")]
            public string Currency { get; set; }

            [Name("Subtotal")]
            public decimal Subtotal { get; set; }

            [Name("Shipping Amount")]
            public decimal ShippingAmount { get; set; }

            [Name("Sales Tax")]
            public decimal SalesTax { get; set; }

            [Name("Total")]
            public decimal Total { get; set; }

            [Name("Buyers Premium Percent")]
            public decimal BuyersPremiumPercent { get; set; }

            [Name("Buyers Premium Amount")]
            public decimal BuyersPremiumAmount { get; set; }

            [Name("MP Commission Percent")]
            public decimal MPCommissionPercent { get; set; }

            [Name("MP Commission Amount")]
            public decimal MPCommissionAmount { get; set; }

            [Name("Seller Paypal Fees")]
            public decimal SellerPaypalFees { get; set; }

            [Name("Relisting Fees")]
            public decimal RelistingFees { get; set; }

            [Name("Comments")]
            public string Comments { get; set; }

            [Name("Last Updated User")]
            public string LastUpdatedUser { get; set; }

            // Shipping Details
            [Name("Shipping First Name")]
            public string ShippingFirstName { get; set; }

            [Name("Shipping Last Name")]
            public string ShippingLastName { get; set; }

            [Name("Shipping Street 1")]
            public string ShippingStreet1 { get; set; }

            [Name("Shipping Street 2")]
            public string ShippingStreet2 { get; set; }

            [Name("Shipping City")]
            public string ShippingCity { get; set; }

            [Name("Shipping State Region")]
            public string ShippingStateRegion { get; set; }

            [Name("Shipping Zip Postal")]
            public string ShippingZipPostal { get; set; }

            [Name("Shipping Country")]
            public string ShippingCountry { get; set; }

            [Name("Shipping Option ID")]
            public int? ShippingOptionId { get; set; }

            [Name("Shipped")]
            public bool Shipped { get; set; }

            [Name("Shipping Details")]
            public string ShippingDetails { get; set; }

            // Billing Details
            [Name("Billing First Name")]
            public string BillingFirstName { get; set; }

            [Name("Billing Last Name")]
            public string BillingLastName { get; set; }

            [Name("Billing Street 1")]
            public string BillingStreet1 { get; set; }

            [Name("Billing Street 2")]
            public string BillingStreet2 { get; set; }

            [Name("Billing City")]
            public string BillingCity { get; set; }

            [Name("Billing State Region")]
            public string BillingStateRegion { get; set; }

            [Name("Billing Zip Postal")]
            public string BillingZipPostal { get; set; }

            [Name("Billing Country")]
            public string BillingCountry { get; set; }

            // Owner Details
            [Name("Owner ID")]
            public int? OwnerId { get; set; }

            [Name("Owner Name")]
            public string OwnerName { get; set; }

            [Name("Owner Street 1")]
            public string OwnerStreet1 { get; set; }

            [Name("Owner Street 2")]
            public string OwnerStreet2 { get; set; }

            [Name("Owner City")]
            public string OwnerCity { get; set; }

            [Name("Owner State Region")]
            public string OwnerStateRegion { get; set; }

            [Name("Owner Zip Postal")]
            public string OwnerZipPostal { get; set; }

            [Name("Owner Country")]
            public string OwnerCountry { get; set; }

            [Name("Owner Username")]
            public string OwnerUsername { get; set; }

            [Name("Owner Email")]
            public string OwnerEmail { get; set; }

            [Name("Owner Phone")]
            public string OwnerPhone { get; set; }

            // Payer Details
            [Name("Payer ID")]
            public int? PayerId { get; set; }

            [Name("Payer Username")]
            public string PayerUsername { get; set; }

            [Name("Payer Email")]
            public string PayerEmail { get; set; }

            [Name("Payer Phone")]
            public string PayerPhone { get; set; }

            // Listing Details
            [Name("Listing ID")]
            public int? ListingId { get; set; }

            [Name("Listing Title")]
            public string ListingTitle { get; set; }

            [Name("Listing Description")]
            public string ListingDescription { get; set; }

            [Name("Listing Status")]
            public string ListingStatus { get; set; }

            [Name("Listing Start Date")]
            public string ListingStartDate { get; set; }

            [Name("Listing End Date")]
            public string ListingEndDate { get; set; }

            [Name("Current Price")]
            public decimal? CurrentPrice { get; set; }

            [Name("Original Price")]
            public decimal? OriginalPrice { get; set; }

            [Name("Primary Category ID")]
            public int? PrimaryCategoryId { get; set; }

            [Name("Listing Type ID")]
            public int ListingTypeId { get; set; }

            [Name("Duration")]
            public int? Duration { get; set; }

            [Name("Current Quantity")]
            public int CurrentQuantity { get; set; }

            [Name("Original Quantity")]
            public int OriginalQuantity { get; set; }

            // Payment Response Details
            [Name("Payment Response ID")]
            public int? PaymentResponseId { get; set; }

            [Name("Payment Status")]
            public string PaymentStatus { get; set; }

            [Name("Payment Provider")]
            public string PaymentProvider { get; set; }

            [Name("Payment Method")]
            public string PaymentMethod { get; set; }

            [Name("Transaction ID")]
            public string TransactionId { get; set; }

            [Name("Payment Amount")]
            public decimal? PaymentAmount { get; set; }

            [Name("Payment Description")]
            public string PaymentDescription { get; set; }

            [Name("Authorization Code")]
            public string AuthorizationCode { get; set; }

            [Name("Payment Date")]
            public string PaymentDate { get; set; }

            [Name("Payment Approved")]
            public bool? PaymentApproved { get; set; }
        }

        public string GenerateCustomInvoiceReport()
        {
            try
            {
                aweFramework context = new aweFramework();

                // Get all invoices with their related data and type = Shipping
                var invoices = context.RWX_Invoices
                    .Where(inv => !inv.DeletedOn.HasValue && inv.Type == "Shipping")
                    .OrderBy(inv => inv.CreatedOn)
                    .ToList();

                var customInvoiceRecords = new List<CustomInvoiceCsvRecord>();

                foreach (var invoice in invoices)
                {
                    try
                    {
                        // Get detailed invoice data using AccountingClient
                        var detailedInvoice = AccountingClient.GetInvoiceByID("admin", invoice.Id);
                        if (detailedInvoice == null) continue;

                        // Get line items for this invoice
                        var lineItems = AccountingClient.GetLineItemsByInvoice("admin", invoice.Id, 0, 0,
                            RainWorx.FrameWorx.Strings.Fields.DateStamp, false).List;

                        // Get payment response for this invoice 
                        // If there are multiple payment responses, and in that there is a Refunded response, we will continue and skip this invoixce
                        var paymentResponse = context.RWX_PaymentResponses
                            .FirstOrDefault(pr => pr.InvoiceId == invoice.Id && pr.Status == "Refunded");

                        if (paymentResponse != null) continue; // Skip if payment response is refunded

                        paymentResponse = context.RWX_PaymentResponses
                            .FirstOrDefault(pr => pr.InvoiceId == invoice.Id);

                        // Get listing details from first line item (if exists)
                        RWX_Listings listing = null;
                        var firstListingId = lineItems
                            .Where(li => li.ListingID.HasValue)
                            .Select(li => li.ListingID.Value)
                            .FirstOrDefault();

                        if (firstListingId != 0)
                        {
                            listing = context.RWX_Listings
                                .FirstOrDefault(l => l.Id == firstListingId);
                        }

                        if (listing == null)
                        {
                            // If no listing found, continue to next invoice
                            continue;
                        }

                        // Get owner user details
                        DTO.User ownerUser = null;
                        if (invoice.OwnerId.HasValue)
                        {
                            try
                            {
                                ownerUser = UserClient.GetUserByID("admin", invoice.OwnerId.Value);
                            }
                            catch { /* Continue if user not found */ }
                        }

                        // Get payer user details
                        DTO.User payerUser = null;
                        if (invoice.PayerId.HasValue)
                        {
                            try
                            {
                                payerUser = UserClient.GetUserByID("admin", invoice.PayerId.Value);
                            }
                            catch { /* Continue if user not found */ }
                        }

                        var record = new CustomInvoiceCsvRecord
                        {
                            // Invoice Details
                            InvoiceId = invoice.Id,
                            InvoiceStatus = invoice.Status ?? "",
                            InvoiceCreatedDate = invoice.CreatedOn.ToString("yyyy-MM-dd HH:mm:ss"),
                            InvoicePaidDate = invoice.PaidDTTM?.ToString("yyyy-MM-dd HH:mm:ss") ?? "",
                            InvoiceType = invoice.Type ?? "",
                            Currency = invoice.Currency ?? "",
                            Subtotal = invoice.Subtotal,
                            ShippingAmount = invoice.ShippingAmount,
                            SalesTax = invoice.SalesTax,
                            Total = invoice.Total,
                            BuyersPremiumPercent = invoice.BuyersPremiumPercent,
                            BuyersPremiumAmount = invoice.BuyersPremiumAmount,
                            MPCommissionPercent = invoice.MPCommissionPercent,
                            MPCommissionAmount = invoice.MPCommissionAmount,
                            SellerPaypalFees = invoice.SellerPaypalFees ?? 0,
                            RelistingFees = invoice.RelistingFees ?? 0,
                            Comments = "",
                            LastUpdatedUser = invoice.LastUpdatedUser ?? "",

                            // Shipping Details
                            ShippingFirstName = invoice.ShippingFirstName ?? "",
                            ShippingLastName = invoice.ShippingLastName ?? "",
                            ShippingStreet1 = invoice.ShippingStreet1 ?? "",
                            ShippingStreet2 = invoice.ShippingStreet2 ?? "",
                            ShippingCity = invoice.ShippingCity ?? "",
                            ShippingStateRegion = invoice.ShippingStateRegion ?? "",
                            ShippingZipPostal = invoice.ShippingZipPostal ?? "",
                            ShippingCountry = invoice.ShippingCountry ?? "",
                            ShippingOptionId = invoice.ShippingOptionId,
                            Shipped = invoice.Shipped,
                            ShippingDetails = invoice.ShippingDetails ?? "",

                            // Billing Details
                            BillingFirstName = invoice.BillingFirstName ?? "",
                            BillingLastName = invoice.BillingLastName ?? "",
                            BillingStreet1 = invoice.BillingStreet1 ?? "",
                            BillingStreet2 = invoice.BillingStreet2 ?? "",
                            BillingCity = invoice.BillingCity ?? "",
                            BillingStateRegion = invoice.BillingStateRegion ?? "",
                            BillingZipPostal = invoice.BillingZipPostal ?? "",
                            BillingCountry = invoice.BillingCountry ?? "",

                            // Owner Details
                            OwnerId = invoice.OwnerId,
                            OwnerName = invoice.OwnerName ?? "",
                            OwnerStreet1 = invoice.OwnerStreet1 ?? "",
                            OwnerStreet2 = invoice.OwnerStreet2 ?? "",
                            OwnerCity = invoice.OwnerCity ?? "",
                            OwnerStateRegion = invoice.OwnerStateRegion ?? "",
                            OwnerZipPostal = invoice.OwnerZipPostal ?? "",
                            OwnerCountry = invoice.OwnerCountry ?? "",
                            OwnerUsername = ownerUser?.UserName ?? "",
                            OwnerEmail = ownerUser?.Email ?? "",
                            OwnerPhone = ownerUser?.PhoneNumber ?? "",

                            // Payer Details
                            PayerId = invoice.PayerId,
                            PayerUsername = payerUser?.UserName ?? "",
                            PayerEmail = payerUser?.Email ?? "",
                            PayerPhone = payerUser?.PhoneNumber ?? "",

                            // Listing Details
                            ListingId = listing?.Id,
                            ListingTitle = listing?.Title ?? "",
                            ListingDescription = "",
                            ListingStatus = listing?.Status ?? "",
                            ListingStartDate = listing?.StartDTTM?.ToString("yyyy-MM-dd HH:mm:ss") ?? "",
                            ListingEndDate = listing?.EndDTTM?.ToString("yyyy-MM-dd HH:mm:ss") ?? "",
                            CurrentPrice = listing?.CurrentPrice,
                            OriginalPrice = listing?.OriginalPrice,
                            PrimaryCategoryId = listing?.PrimaryCategoryId,
                            ListingTypeId = listing?.ListingTypeId ?? 0,
                            Duration = listing?.Duration,
                            CurrentQuantity = listing?.CurrentQuantity ?? 0,
                            OriginalQuantity = listing?.OriginalQuantity ?? 0,

                            // Payment Response Details
                            PaymentResponseId = paymentResponse?.Id,
                            PaymentStatus = paymentResponse?.Status ?? "",
                            PaymentProvider = paymentResponse?.Provider ?? "",
                            PaymentMethod = paymentResponse?.Method ?? "",
                            TransactionId = paymentResponse?.TransactionID ?? "",
                            PaymentAmount = paymentResponse?.Amount,
                            PaymentDescription = paymentResponse?.Description ?? "",
                            AuthorizationCode = paymentResponse?.AuthorizationCode ?? "",
                            PaymentDate = paymentResponse?.DateStamp.ToString("yyyy-MM-dd HH:mm:ss") ?? "",
                            PaymentApproved = paymentResponse?.Approved
                        };

                        customInvoiceRecords.Add(record);
                    }
                    catch (Exception ex)
                    {
                        // Log error but continue processing other invoices
                        Debug.WriteLine($"Error processing invoice {invoice.Id}: {ex.Message}");
                        continue;
                    }
                }

                // Generate CSV file
                var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
                var reportName = $"CustomInvoices_{timestamp}.csv";
                var folder = "~/App_Data/Reports";
                var filePath = HostingEnvironment.MapPath(Path.Combine(folder, reportName));

                // Ensure directory exists
                Directory.CreateDirectory(Path.GetDirectoryName(filePath));

                using (var writer = new StreamWriter(filePath))
                using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
                {
                    csv.WriteRecords(customInvoiceRecords);
                }

                return reportName;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error generating custom invoice report: {ex.Message}");
                throw;
            }
        }
        #endregion

        #region Duplicate Address User Check

        /// <summary>
        /// Checks for duplicate addresses by zip code and sends a report.
        /// </summary>
        private void CheckDuplicateAddresses()
        {
            var ctxss = new aweFramework();

            // Get all the zip codes which are repeated more than once, excluding 78624
            var duplicateZipCodes = ctxss.RWX_Addresses
                .Join(ctxss.RWX_Users,
                    address => address.UserId,
                    user => user.Id,
                    (address, user) => new { Address = address, User = user })
                .Where(joined => joined.User.IsActive && joined.Address.ZipPostal != "78624")
                .GroupBy(joined => joined.Address.ZipPostal)
                .Where(group => group.Count() > 1)
                .Select(group => new
                {
                    ZipPostal = group.Key,
                    Addresses = group.Select(joined => joined.Address).ToList(),
                    Users = group.Select(joined => joined.User).ToList()
                })
                .ToList();

            var flaggedAddressesByZipCode = new List<FuzzyMatchResult>();

            // Perform fuzzy match within each group
            foreach (var group in duplicateZipCodes)
            {
                var addresses = group.Addresses;
                var users = group.Users;

                for (int i = 0; i < addresses.Count; i++)
                {
                    for (int j = i + 1; j < addresses.Count; j++)
                    {
                        var address1 = addresses[i];
                        var address2 = addresses[j];

                        if (address1.UserId == address2.UserId) continue;

                        var street1 = $"{address1.Street1} {address1.Street2}";
                        var street2 = $"{address2.Street1} {address2.Street2}";

                        var matchScore = street1.FuzzyMatch(street2);

                        if (matchScore > 0.98)
                        {
                            var user1 = users.First(u => u.Id == address1.UserId);
                            var user2 = users.First(u => u.Id == address2.UserId);

                            var result = new FuzzyMatchResult
                            {
                                User1Name = $"{user1.UserName}",
                                User2Name = $"{user2.UserName}",
                                User1Address = $"{address1.Street1} {address1.Street2} {address1.City} {address1.StateRegion} {address1.CountryId}",
                                User2Address = $"{address2.Street1} {address2.Street2} {address2.City} {address2.StateRegion} {address2.CountryId}",
                                User1Link = $"{SiteClient.Settings[SiteProperties.SecureURL]}/Admin/UserSummary/{address1.UserId}",
                                User2Link = $"{SiteClient.Settings[SiteProperties.SecureURL]}/Admin/UserSummary/{address2.UserId}",
                                address1ID = address1.Id.ToString(),
                                address2ID = address2.Id.ToString(),
                                zipPostal = address1.ZipPostal,
                                MatchScore = matchScore
                            };

                            flaggedAddressesByZipCode.Add(result);
                        }
                    }
                }
            }

            var reportName = GenerateCsvReportForDuplicates(flaggedAddressesByZipCode);

            SendDuplicateAddressesReport(reportName);
        }

        private string GenerateCsvReportForDuplicates(List<FuzzyMatchResult> flaggedAddressesByZipCode)
        {
            const string folder = "~/Duplicates";
            var reportName = "duplicate-addresses.csv";
            var fileName = $"{folder}/{reportName}";

            var folderPath = HostingEnvironment.MapPath(folder);
            if (!Directory.Exists(folderPath))
            {
                Directory.CreateDirectory(folderPath);
            }

            var filePath = HostingEnvironment.MapPath(fileName);
            using (var writer = new StreamWriter(filePath))
            using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
            {
                csv.WriteRecords(flaggedAddressesByZipCode);
            }

            return reportName;
        }

        private void SendDuplicateAddressesReport(string reportName)
        {
            const string folder = "~/Duplicates";
            var fileName = $"{folder}/{reportName}";
            var filePath = HostingEnvironment.MapPath(fileName);
            var attachmentBase64 = Convert.ToBase64String(File.ReadAllBytes(filePath));

            var emails = new List<string> {
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
            };
            var ccEmails = new List<string> {
                // "<EMAIL>",
                // "<EMAIL>",
                // "<EMAIL>",
                "<EMAIL>"
            };
            var bccEmails = new List<string> { };
            var subject = "Complete Duplicate Addresses Report";
            var body = "Hi Team,\n\nPFA the complete report of duplicate addresses.\n\nRegards,\nMagnolia Pearl Team.\n\nPlease note: This is an automated email. Please reach out to the dev team for any queries.";
            const string mimeType = "text/csv"; // MIME type for CSV files

            var attachments = new List<(string Base64, string FileName, string MimeType)>
            {
                (attachmentBase64, reportName, mimeType)
            };

            var ssClient = new ShipStationClient();
            ssClient.SendEmailWithAttachments(emails, ccEmails, bccEmails, subject, body, attachments);
        }


        #endregion

        #region Seller Paypal Fees Fill


        /// <summary>
        /// Update the SellerPaypalFees in the RWX_Invoices table from 2023-04-12 to yesterday
        /// </summary>
        private void FillSellerPaypalFees()
        {
            var firstDay = new DateTime(2023, 4, 12);
            var lastDay = DateTime.Today.AddDays(-1);
            UpdateSellerPaypalFees(firstDay, lastDay);
        }

        /// <summary>
        /// Update the SellerPaypalFees in the RWX_Invoices table daily for the last 3 days
        /// </summary>
        private void FillSellerPaypalFeesDaily()
        {
            var lastDay = DateTime.Today;
            var firstDay = lastDay.AddDays(-3);
            UpdateSellerPaypalFees(firstDay, lastDay);
        }

        /// <summary>
        /// Update the SellerPaypalFees in the RWX_Invoices table
        /// </summary>
        /// <param name="firstDay"></param>
        /// <param name="lastDay"></param>
        private void UpdateSellerPaypalFees(DateTime firstDay, DateTime lastDay)
        {
            aweFramework context = new aweFramework();
            List<RWX_Invoices> invoices = context.RWX_Invoices.Where(inv => inv.Status == "Paid" && inv.PaidDTTM >= firstDay && inv.PaidDTTM <= lastDay &&
                                                                            (inv.SellerPaypalFees == 0 || inv.SellerPaypalFees == null)).ToList();
            // Previous Data
            List<RWX_Invoices> previousData = invoices;
            foreach (var invoice in invoices)
            {
                AccountController ac = new AccountController();
                invoice.SellerPaypalFees = ac.getPaypalFees(invoice.Id);
            }
            context.SaveChanges();
            // Log the changes with invoice id, previous value and new value in a single string
            foreach (var invoice in invoices)
            {
                var previous = previousData.Find(inv => inv.Id == invoice.Id);
                LogManager.WriteLog($"Invoice ID: {invoice.Id}, Previous Value: {previous.SellerPaypalFees}, New Value: {invoice.SellerPaypalFees}", "Global.asax.fillSellerPaypalFeesDaily", "global.asax.cs"
                    , TraceEventType.Information, null, null, new Dictionary<string, object>() { });
            }
        }

        #endregion
    }
}
